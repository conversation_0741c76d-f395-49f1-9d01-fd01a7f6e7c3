# 🚀 Wiz Aroma Food Delivery System v2.1 Deployment Guide

This document provides detailed instructions for deploying the Wiz Aroma Food Delivery System v2.1 with Firebase-first architecture and automatic data lifecycle management in various environments.

## Local Deployment

### Prerequisites

- Python 3.8 or higher
- Git
- Telegram bot tokens (see [BOT_SETUP.md](BOT_SETUP.md))

### Steps

1. **Clone the repository**:

   ```bash
   git clone https://github.com/Mih-Nig-Afe/Wiz-Aroma-Food-Delivery.git
   cd Wiz-Aroma-Food-Delivery
   ```

2. **Create a virtual environment** (optional but recommended):

   ```bash
   python -m venv venv

   # On Windows
   venv\Scripts\activate

   # On macOS/Linux
   source venv/bin/activate
   ```

3. **Install dependencies**:

   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment variables**:
   - Copy `.env.example` to `.env`
   - Fill in your bot tokens and configuration:

     ```
     # Bot Tokens
     BOT_TOKEN=your_user_bot_token
     ADMIN_BOT_TOKEN=your_admin_bot_token
     FINANCE_BOT_TOKEN=your_finance_bot_token
     MAINTENANCE_BOT_TOKEN=your_maintenance_bot_token

     # Chat IDs
     ADMIN_CHAT_IDS=["your_admin_id"]
     FINANCE_CHAT_ID=your_finance_id
     MAINTENANCE_CHAT_ID=your_maintenance_id

     # Payment Information
     TELEBIRR_PHONE=your_telebirr_phone
     TELEBIRR_NAME=your_telebirr_name
     CBE_ACCOUNT_NUMBER=your_cbe_account_number
     CBE_ACCOUNT_NAME=your_cbe_account_name
     BOA_ACCOUNT_NUMBER=your_boa_account_number
     BOA_ACCOUNT_NAME=your_boa_account_name

     # Support Information
     SUPPORT_PHONE_1=your_support_phone_1
     SUPPORT_PHONE_2=your_support_phone_2
     SUPPORT_TELEGRAM=your_support_telegram

     # Firebase Configuration (if using Firebase)
     FIREBASE_DATABASE_URL=https://your-project-id.firebaseio.com
     # Either use the JSON file or paste the credentials JSON directly
     # FIREBASE_CREDENTIALS={"type":"service_account",...}

     # Logging
     LOG_LEVEL=INFO

     # Test Mode (set to False for production)
     TEST_MODE=False
     ```

5. **Configure Firebase** (if using Firebase):
   - Follow the instructions in [FIREBASE_SETUP.md](FIREBASE_SETUP.md)
   - You can either:
     - Download your Firebase service account key as `firebase-credentials.json` and place it in the root directory
     - Or, convert the key to a JSON string and set it as the `FIREBASE_CREDENTIALS` environment variable (recommended for production)

6. **Initialize data directories**:

   ```bash
   mkdir -p data_files
   ```

7. **Run the bots**:

   It's recommended to run each bot separately to avoid conflicts:

   ```bash
   # Run user bot
   python main.py --bot user

   # Run admin bot
   python main.py --bot admin

   # Run finance bot
   python main.py --bot finance

   # Run maintenance bot
   python main.py --bot maintenance
   ```

   Alternatively, run all bots at once:

   ```bash
   python main.py --bot all
   ```

   **Note**: Running all bots simultaneously may cause API conflicts if they're not properly configured with different tokens.

## Railway Deployment

[Railway](https://railway.app/) is a platform that makes it easy to deploy your applications.

### Prerequisites

- GitHub account
- Railway account
- Telegram bot tokens

### Steps

1. **Fork the repository** to your GitHub account.

2. **Create a new project in Railway**:
   - Go to [Railway Dashboard](https://railway.app/dashboard)
   - Click "New Project"
   - Select "Deploy from GitHub repo"
   - Select your forked repository

3. **Configure environment variables**:
   - In your Railway project, go to the "Variables" tab
   - Add the following environment variables:

     ```
     BOT_TOKEN=your_user_bot_token
     ADMIN_BOT_TOKEN=your_admin_bot_token
     FINANCE_BOT_TOKEN=your_finance_bot_token
     MAINTENANCE_BOT_TOKEN=your_maintenance_bot_token
     ADMIN_CHAT_IDS=["your_admin_id"]
     FINANCE_CHAT_ID=your_finance_id
     MAINTENANCE_CHAT_ID=your_maintenance_id
     TELEBIRR_PHONE=your_telebirr_phone
     TELEBIRR_NAME=your_telebirr_name
     CBE_ACCOUNT_NUMBER=your_cbe_account_number
     CBE_ACCOUNT_NAME=your_cbe_account_name
     BOA_ACCOUNT_NUMBER=your_boa_account_number
     BOA_ACCOUNT_NAME=your_boa_account_name
     SUPPORT_PHONE_1=your_support_phone_1
     SUPPORT_PHONE_2=your_support_phone_2
     SUPPORT_TELEGRAM=your_support_telegram
     LOG_LEVEL=INFO
     TEST_MODE=False
     
     # Firebase Configuration (if using Firebase)
     FIREBASE_DATABASE_URL=https://your-project-id.firebaseio.com
     FIREBASE_CREDENTIALS={"type":"service_account",...}
     ```

   - **IMPORTANT**: For the `FIREBASE_CREDENTIALS` variable, paste the entire contents of your Firebase service account JSON file as a single line. This is more secure than uploading the JSON file to your repository.

4. **Deploy the application**:
   - Railway will automatically detect the Procfile and start the deployment
   - The Procfile contains the command to run the application:

     ```
     web: python main.py --bot all
     ```

5. **Verify deployment**:
   - Check the deployment logs to ensure all bots are running
   - Test each bot by sending the `/start` command

## Heroku Deployment

[Heroku](https://www.heroku.com/) is another popular platform for deploying applications.

### Prerequisites

- GitHub account
- Heroku account
- Telegram bot tokens

### Steps

1. **Fork the repository** to your GitHub account.

2. **Create a new app in Heroku**:
   - Go to [Heroku Dashboard](https://dashboard.heroku.com/)
   - Click "New" > "Create new app"
   - Enter a name for your app and select a region
   - Click "Create app"

3. **Connect to GitHub**:
   - In your Heroku app, go to the "Deploy" tab
   - Select "GitHub" as the deployment method
   - Connect to your GitHub account
   - Search for your forked repository and click "Connect"

4. **Configure environment variables**:
   - In your Heroku app, go to the "Settings" tab
   - Click "Reveal Config Vars"
   - Add the same environment variables as listed in the Railway deployment section

5. **Add a Procfile**:
   - The repository already includes a Procfile with the following content:

     ```
     web: python main.py --bot all
     ```

6. **Deploy the application**:
   - In the "Deploy" tab, scroll down to "Manual deploy"
   - Select the branch you want to deploy (usually "main")
   - Click "Deploy Branch"

7. **Verify deployment**:
   - Check the deployment logs to ensure all bots are running
   - Test each bot by sending the `/start` command

## Docker Deployment

You can also deploy the application using Docker.

### Prerequisites

- Docker
- Docker Compose (optional)
- Telegram bot tokens

### Steps

1. **Create a Dockerfile**:

   ```dockerfile
   FROM python:3.9-slim

   WORKDIR /app

   COPY requirements.txt .
   RUN pip install --no-cache-dir -r requirements.txt

   COPY . .

   CMD ["python", "main.py", "--bot", "all"]
   ```

2. **Create a docker-compose.yml file** (optional):

   ```yaml
   version: '3'

   services:
     bot:
       build: .
       env_file:
         - .env
       volumes:
         - ./data_files:/app/data_files
   ```

3. **Build and run the Docker container**:

   Using Docker:

   ```bash
   docker build -t wiz-aroma-food-delivery .
   docker run -d --env-file .env -v $(pwd)/data_files:/app/data_files wiz-aroma-food-delivery
   ```

   Using Docker Compose:

   ```bash
   docker-compose up -d
   ```

4. **Verify deployment**:
   - Check the container logs to ensure all bots are running:

     ```bash
     docker logs <container_id>
     ```

   - Test each bot by sending the `/start` command

## Troubleshooting

### Common Issues

1. **"Conflict: terminated by other getUpdates request" Error**:
   - This error occurs when multiple instances of the same bot are running
   - Make sure each bot has a unique token
   - Stop all running bots and restart them individually
   - Use the `--bot` parameter to specify which bot to run

2. **"Chat not found" Error in Finance Bot**:
   - This error occurs when the Finance Bot tries to send a message to a chat that doesn't exist
   - Make sure the user with ID specified in `FINANCE_CHAT_ID` has started a chat with the Finance Bot
   - See [FINANCE_BOT_SETUP.md](FINANCE_BOT_SETUP.md) for more details

3. **Unicode Emoji Errors in Windows Console**:
   - Windows console has issues with Unicode emoji characters in logs
   - This can cause `UnicodeEncodeError` with charmap codec
   - Run the bot in a terminal that supports Unicode, or redirect logs to a file

4. **Data Files Not Found**:
   - Make sure the `data_files` directory exists
   - The application should create it automatically, but you can create it manually if needed
   - Check file permissions to ensure the application can write to the directory

### Checking Logs

To check the logs for troubleshooting:

```bash
# View the log file
cat bot.log

# View the last 100 lines of the log file
tail -n 100 bot.log

# Follow the log file in real-time
tail -f bot.log
```

### Restarting the Bots

If you need to restart the bots:

```bash
# Stop all running bots
taskkill /f /im python.exe /fi "WINDOWTITLE eq Telegram Bot"

# Start the bots again
python main.py --bot user
python main.py --bot admin
python main.py --bot finance
python main.py --bot maintenance
```

## Backup and Restore

### Backing Up Data

It's important to regularly back up your data files:

```bash
# Create a backup directory
mkdir -p backups

# Create a timestamped backup
timestamp=$(date +%Y%m%d_%H%M%S)
cp -r data_files backups/data_files_$timestamp
```

### Restoring Data

To restore data from a backup:

```bash
# Stop all running bots
taskkill /f /im python.exe /fi "WINDOWTITLE eq Telegram Bot"

# Restore from a specific backup
cp -r backups/data_files_20230101_120000/* data_files/

# Start the bots again
python main.py --bot user
python main.py --bot admin
python main.py --bot finance
python main.py --bot maintenance
```

## Monitoring

To monitor the application in production:

1. **Check the logs regularly**:

   ```bash
   tail -f bot.log
   ```

2. **Set up alerts** for critical errors:
   - You can use a log monitoring service like Papertrail or Loggly
   - Configure alerts for keywords like "ERROR" or "CRITICAL"

3. **Monitor system resources**:
   - CPU usage
   - Memory usage
   - Disk space (especially for data files)

4. **Implement health checks**:
   - Add a `/health` command to each bot
   - Set up a monitoring service to periodically check if the bots are responsive
