# Troubleshooting Railway Firebase Issues

## Problem
Maintenance bot price update works locally but fails on Railway deployment with "Failed to update menu item price."

## Enhanced Debugging Features Added

### 1. Improved Error Handling
- Enhanced logging in `update_menu_item()` function
- Better error messages in maintenance bot
- Rollback functionality for failed updates
- Detailed Firebase operation logging

### 2. Firebase Diagnostics Command
Use `/firebase_diagnostics` command in the maintenance bot to get detailed information about:
- Environment variables status
- Firebase initialization status
- Connectivity test results
- Credentials source verification
- Specific error messages and recommendations

### 3. Test Script
Run `python test_firebase_connection.py` to test Firebase connectivity locally or in deployment.

## Common Causes and Solutions

### 1. Environment Variables Issues

**Check these in Railway:**
- `FIREBASE_DATABASE_URL` - Must be set to your Firebase Realtime Database URL
- `FIREBASE_CREDENTIALS` - Must contain the complete Firebase service account JSON

**Solution:**
```bash
# In Railway, set these environment variables:
FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/
FIREBASE_CREDENTIALS={"type":"service_account","project_id":"your-project-id",...}
```

### 2. Firebase Credentials Format

**Common Issue:** Malformed JSON in `FIREBASE_CREDENTIALS`

**Solution:**
1. Download your Firebase service account key as JSON
2. Minify the JSON (remove all whitespace and newlines)
3. Paste the entire JSON as a single line in Railway environment variables

**Example:**
```json
{"type":"service_account","project_id":"wiz-aroma-123","private_key_id":"abc123","private_key":"-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----\n","client_email":"*******","client_id":"*********","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xyz%40wiz-aroma-123.iam.gserviceaccount.com"}
```

### 3. Firebase Database Rules

**Check your Firebase Realtime Database rules:**
```json
{
  "rules": {
    ".read": true,
    ".write": true
  }
}
```

**For production, use more restrictive rules:**
```json
{
  "rules": {
    ".read": "auth != null",
    ".write": "auth != null"
  }
}
```

### 4. Network/Connectivity Issues

**Symptoms:**
- Connection timeouts
- Network errors in logs

**Solutions:**
1. Check Railway's network connectivity
2. Verify Firebase service status
3. Check for firewall/proxy issues

### 5. JWT Token Issues

**Symptoms:**
- "invalid_grant" errors
- JWT token expired messages

**Solutions:**
1. Regenerate Firebase service account key
2. Update `FIREBASE_CREDENTIALS` with new key
3. Restart Railway deployment

## Debugging Steps

### Step 1: Check Railway Logs
1. Go to Railway dashboard
2. Select your project
3. Check deployment logs for Firebase-related errors

### Step 2: Use Diagnostic Command
1. Send `/firebase_diagnostics` to maintenance bot
2. Review the diagnostic report
3. Follow the recommendations provided

### Step 3: Test Locally
1. Run `python test_firebase_connection.py`
2. Compare results with Railway environment
3. Identify differences in configuration

### Step 4: Verify Environment Variables
1. Check Railway environment variables
2. Ensure `FIREBASE_CREDENTIALS` is properly formatted JSON
3. Verify `FIREBASE_DATABASE_URL` is correct

### Step 5: Test Firebase Console
1. Go to Firebase Console
2. Check Realtime Database
3. Verify data structure matches expectations
4. Test read/write permissions

## Quick Fixes

### Fix 1: Redeploy with Fresh Credentials
1. Generate new Firebase service account key
2. Update `FIREBASE_CREDENTIALS` in Railway
3. Redeploy the application

### Fix 2: Check Database Structure
1. Verify menu data exists in Firebase
2. Check restaurant and menu item IDs
3. Ensure data format matches code expectations

### Fix 3: Enable Debug Logging
Add to Railway environment variables:
```
LOG_LEVEL=DEBUG
```

## Testing the Fix

### 1. Local Testing
```bash
python test_firebase_connection.py
```

### 2. Deployment Testing
1. Use `/firebase_diagnostics` command
2. Try updating a menu item price
3. Check Railway logs for detailed error messages

### 3. Verification
1. Confirm menu item price updates successfully
2. Verify data persists in Firebase
3. Test with multiple menu items

## Prevention

### 1. Environment Variable Validation
- Always validate JSON format before setting
- Use environment variable templates
- Document required variables

### 2. Monitoring
- Set up Firebase monitoring
- Monitor Railway deployment logs
- Implement health checks

### 3. Testing
- Test Firebase operations in staging environment
- Validate credentials before production deployment
- Regular connectivity tests

## Support

If issues persist after following this guide:

1. Check Railway deployment logs for specific error messages
2. Use the enhanced error messages in the updated code
3. Run the diagnostic command for detailed troubleshooting information
4. Verify Firebase service account permissions and database rules

The enhanced error handling and logging should provide much more specific information about what's failing in the Railway deployment environment.
