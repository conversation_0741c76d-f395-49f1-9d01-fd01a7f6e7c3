# Docker Setup Guide for Wiz Aroma Delivery Bot

This guide will help you run the Wiz Aroma Food Delivery system using Docker and Docker Compose.

## Prerequisites

- Docker and Docker Compose installed on your system
- All required bot tokens from Telegram @BotFather
- Firebase project configured (see FIREBASE_SETUP.md)

## Quick Start

### 1. Create Environment File

Create a `.env` file in the root directory with your configuration. The Docker Compose setup automatically loads all variables from this file:

```bash
# Bot Tokens - REQUIRED
# Get these from @BotFather on Telegram
BOT_TOKEN=your_user_bot_token
ADMIN_BOT_TOKEN=your_admin_bot_token
FINANCE_BOT_TOKEN=your_finance_bot_token
MAINTENANCE_BOT_TOKEN=your_maintenance_bot_token
MANAGEMENT_BOT_TOKEN=your_management_bot_token
ORDER_TRACK_BOT_TOKEN=your_order_track_bot_token
DELIVERY_BOT_TOKEN=your_delivery_bot_token

# System Admin Configuration - REQUIRED
SYSTEM_ADMIN_ID=your_telegram_user_id

# Chat IDs for different bot functions
# Use @userinfobot to get your Telegram ID
ADMIN_CHAT_IDS=["your_admin_id"]
FINANCE_CHAT_ID=your_finance_id
MAINTENANCE_CHAT_ID=your_maintenance_id
ORDER_TRACK_BOT_AUTHORIZED_IDS=[your_admin_id]

# Payment Information
TELEBIRR_PHONE=your_telebirr_phone
TELEBIRR_NAME=your_telebirr_name
CBE_ACCOUNT_NUMBER=your_cbe_account_number
CBE_ACCOUNT_NAME=your_cbe_account_name
BOA_ACCOUNT_NUMBER=your_boa_account_number
BOA_ACCOUNT_NAME=your_boa_account_name

# Support Information
SUPPORT_PHONE_1=your_support_phone_1
SUPPORT_PHONE_2=your_support_phone_2
SUPPORT_TELEGRAM=your_support_telegram

# Firebase Configuration
# Get these from your Firebase project settings
FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/
# Either use the JSON file or paste the credentials JSON directly (recommended for Docker)
# FIREBASE_CREDENTIALS={"type":"service_account","project_id":"your-project","private_key_id":"..."}

# Application Configuration
LOG_LEVEL=INFO
TEST_MODE=False
```

### 2. Firebase Credentials Setup

You have two options for Firebase authentication:

#### Option A: JSON File (Easier for local development)
1. Download your Firebase service account key as `firebase-credentials.json`
2. Place it in the project root directory
3. The Docker compose will mount it automatically

#### Option B: Environment Variable (Recommended for production)
1. Convert your Firebase service account JSON to a single line string
2. Set it as the `FIREBASE_CREDENTIALS` environment variable in your `.env` file

### 3. Build and Run

#### Run All Bots in One Container (Default)
```bash
# Build and start the application
docker compose up -d

# View logs
docker compose logs -f

# Stop the application
docker compose down
```

#### Run Individual Bots in Separate Containers
```bash
# Start individual bot services
docker compose --profile separate-bots up -d

# This will run:
# - wiz-aroma-user-bot
# - wiz-aroma-admin-bot
# (You can add more services in docker compose.yml for other bots)

# View logs for specific bot
docker compose logs -f wiz-aroma-user-bot

# Stop individual bots
docker compose --profile separate-bots down
```

## Docker Commands Reference

### Basic Operations
```bash
# Build the image
docker compose build

# Start in background
docker compose up -d

# Start with logs visible
docker compose up

# Stop all services
docker compose down

# Restart a specific service
docker compose restart wiz-aroma-bot

# View logs
docker compose logs -f [service_name]

# Access container shell
docker compose exec wiz-aroma-bot bash
```

### Maintenance Commands
```bash
# Remove all containers and volumes (CAUTION: This will delete data)
docker compose down -v

# Rebuild without cache
docker compose build --no-cache

# Update to latest changes
git pull
docker compose build
docker compose up -d
```

## Configuration Options

### Environment Variables

All environment variables are automatically loaded from your `.env` file. Key variables include:

- **BOT_TOKEN**: Primary user bot token (required)
- **SYSTEM_ADMIN_ID**: Your Telegram user ID (required)
- **FIREBASE_DATABASE_URL**: Your Firebase Realtime Database URL
- **LOG_LEVEL**: Logging level (DEBUG, INFO, WARNING, ERROR)
- **TEST_MODE**: Set to true for testing without tokens

### Volume Mounts

- `./data_files:/app/data_files` - Persistent data storage
- `./firebase-credentials.json:/app/firebase-credentials.json:ro` - Firebase auth (read-only)

### Health Checks

The container includes health checks that monitor the Python process. You can check the health status:

```bash
docker compose ps
```

## Troubleshooting

### Common Issues

1. **Container fails to start**
   ```bash
   # Check logs for error details
   docker compose logs wiz-aroma-bot
   ```

2. **Bot tokens not working**
   - Verify your `.env` file has correct tokens
   - Ensure no extra spaces or quotes around tokens
   - Check that bots are created via @BotFather

3. **Firebase connection issues**
   - Verify FIREBASE_DATABASE_URL is correct
   - Check Firebase credentials file exists and is readable
   - Ensure Firebase rules allow your service account access

4. **Environment variables not loading**
   - Ensure `.env` file is in the same directory as `docker compose.yml`
   - Check that `.env` file has no syntax errors
   - Verify file permissions allow reading

5. **Permission issues**
   ```bash
   # Fix data directory permissions
   sudo chown -R 1000:1000 ./data_files
   ```

### Viewing Application Logs

```bash
# All logs
docker compose logs -f

# Last 100 lines
docker compose logs --tail=100

# Specific service logs
docker compose logs -f wiz-aroma-bot
```

### Container Management

```bash
# Check container status
docker compose ps

# Check resource usage
docker stats

# Access container
docker compose exec wiz-aroma-bot bash

# Run specific bot manually
docker compose exec wiz-aroma-bot python main.py --bot user
```

## Production Deployment

For production deployment:

1. Set `TEST_MODE=False` in your `.env` file
2. Use strong, unique bot tokens
3. Set up proper Firebase security rules
4. Consider using Docker Swarm or Kubernetes for scaling
5. Set up monitoring and log aggregation
6. Regular backups of data_files volume

## Security Notes

- Never commit your `.env` file to version control
- Use read-only mounts for sensitive files like Firebase credentials
- Run containers as non-root user (already configured)
- Regularly update base images and dependencies
- Monitor logs for security issues

## Support

If you encounter issues:

1. Check the application logs: `docker compose logs -f`
2. Verify your environment configuration
3. Ensure all required tokens and credentials are set
4. Check Firebase connectivity and permissions

For more information, see the main project documentation in README.md and other setup guides. 