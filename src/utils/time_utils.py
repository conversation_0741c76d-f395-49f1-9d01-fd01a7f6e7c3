'''
Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
Date: 2025-09-19 20:34:04
LastEditors: <PERSON><PERSON><PERSON><PERSON> Nigatu <EMAIL>
LastEditTime: 2025-09-25 12:42:53
FilePath: /WA HU/src/utils/time_utils.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
"""
Time utility functions for the Wiz Aroma Food Delivery system.
"""

import datetime
import logging


def is_open_now() -> bool:
    """Check if the bot is currently open based on working hours."""
    try:
        # Properly get Ethiopian local time (EAT, UTC+3)
        # Get UTC time using the recommended non-deprecated approach
        utc_now = datetime.datetime.now(datetime.timezone.utc)

        # Add 3 hours to get Ethiopian time
        ethiopia_offset = datetime.timedelta(hours=3)
        ethiopia_now = utc_now + ethiopia_offset

        current_time = ethiopia_now.time()
        current_day = ethiopia_now.weekday()  # Monday is 0 and Sunday is 6

        # For debugging
        logging.info(f"UTC time: {utc_now}, Ethiopian time: {ethiopia_now}")
        logging.info(
            f"Current day in Ethiopia: {current_day}, Current time in Ethiopia: {current_time}"
        )

        # Define working hours in Ethiopian local time
        # Lunch Service: 5:30 AM - 7:30 AM (05:30 - 07:30 in 24-hour format)
        # Dinner Service: 10:30 PM - 2:00 AM (22:30 - 02:00 in 24-hour format, spans midnight)
        lunch_start = datetime.time(5, 30)   # 5:30 AM (05:30)
        lunch_end = datetime.time(7, 30)     # 7:30 AM (07:30)
        dinner_start = datetime.time(22, 30) # 10:30 PM (22:30)

        # For dinner service that spans midnight (10:30 PM - 2:00 AM next day)
        # We check if it's after 10:30 PM today OR before 2:00 AM (early morning of next day)
        early_morning_end = datetime.time(2, 0)  # 2:00 AM (02:00) - end of dinner service

        # Check if current time is within working hours
        # Lunch service: 5:30 AM - 7:30 AM
        # Dinner service: 10:30 PM - 11:59 PM OR 12:00 AM - 2:00 AM
        if (lunch_start <= current_time <= lunch_end) or \
           (current_time >= dinner_start) or \
           (current_time <= early_morning_end):
            logging.info(
                f"Within working hours. Day: {current_day}, Time: {current_time}"
            )
            return True

        return False
    except Exception as e:
        logging.error(f"Error in is_open_now function: {e}")
        # In case of an error, default to open to avoid blocking users
        return True


def generate_order_number(user_id, order_count=None):
    """Generate unique order number in format USERID_YYMMDDHHMM_XXXX
    where:
    - USERID is the Telegram user ID
    - YYMMDDHHMM is the date and time (2-digit year, month, day, hour, minute)
    - XXXX is the incremental count for that user"""
    from src.data_models import user_order_counts

    # Get or initialize user's order count
    if order_count is None:
        user_order_counts[user_id] = user_order_counts.get(user_id, 0) + 1
        order_count = user_order_counts[user_id]

    # Get Ethiopian local time (EAT, UTC+3)
    utc_now = datetime.datetime.now(datetime.timezone.utc)
    ethiopia_offset = datetime.timedelta(hours=3)
    ethiopia_now = utc_now + ethiopia_offset

    # Format the current time in Ethiopian time
    date_time_str = ethiopia_now.strftime("%y%m%d%H%M")  # Using Ethiopian time

    # Format the order count to 4 digits
    count_str = str(order_count).zfill(4)

    # Combine all parts with user_id
    return f"{user_id}_{date_time_str}_{count_str}"
