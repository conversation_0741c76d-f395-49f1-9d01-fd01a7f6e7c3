"""
Firebase-First Business Data Management for Wiz-Aroma System
Ensures all business data operations use Firebase as primary storage
with temporary local caching only during active processing.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from src.firebase_db import get_data, set_data, update_data, delete_data
from src.config import logger
from src.utils.temp_data_manager import (
    cleanup_business_data_after_order_completion,
    cleanup_user_session_data
)


class FirebaseBusinessDataManager:
    """
    Manages business data with Firebase-first approach.
    Ensures all persistent business data is stored in Firebase
    with temporary local caching only during active processing.
    """
    
    def __init__(self):
        self._cache_timeout = 300  # 5 minutes cache timeout
        self._last_cache_update = {}
    
    # ========================================================================
    # ORDER MANAGEMENT
    # ========================================================================
    
    def create_order(self, user_id: int, order_data: Dict[str, Any]) -> bool:
        """
        Create a new order in Firebase and cache temporarily.
        
        Args:
            user_id: User ID creating the order
            order_data: Order data to store
            
        Returns:
            True if order created successfully
        """
        try:
            user_id_str = str(user_id)
            order_number = order_data.get('order_number')
            
            if not order_number:
                logger.error("Cannot create order without order_number")
                return False
            
            # Store in Firebase immediately
            firebase_path = f"active_orders/{order_number}"
            firebase_success = set_data(firebase_path, order_data)
            
            if firebase_success:
                # Cache temporarily for performance
                from src.data_models import temp_orders
                temp_orders[user_id_str] = order_data
                
                logger.info(f"Created order {order_number} in Firebase and cached locally")
                return True
            else:
                logger.error(f"Failed to create order {order_number} in Firebase")
                return False
                
        except Exception as e:
            logger.error(f"Error creating order: {e}")
            return False
    
    def update_order_status(self, user_id: int, status: str) -> bool:
        """
        Update order status in Firebase and cache.
        
        Args:
            user_id: User ID
            status: New order status
            
        Returns:
            True if status updated successfully
        """
        try:
            user_id_str = str(user_id)
            
            # Update in Firebase
            firebase_path = f"order_status/{user_id_str}"
            firebase_success = set_data(firebase_path, status)
            
            if firebase_success:
                # Cache temporarily
                from src.data_models import temp_order_status
                temp_order_status[user_id_str] = status
                
                logger.debug(f"Updated order status for user {user_id} to {status}")
                return True
            else:
                logger.error(f"Failed to update order status in Firebase for user {user_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating order status: {e}")
            return False
    
    def complete_order(self, order_number: str, user_id: int) -> bool:
        """
        Complete an order and clean up all temporary data.
        
        Args:
            order_number: Order number to complete
            user_id: User ID associated with the order
            
        Returns:
            True if order completed successfully
        """
        try:
            # Move order from active to completed in Firebase
            active_order_data = get_data(f"active_orders/{order_number}")
            
            if active_order_data:
                # Add completion timestamp
                active_order_data['completed_at'] = datetime.now().isoformat()
                active_order_data['status'] = 'COMPLETED'
                
                # Store in completed orders
                completed_success = set_data(f"completed_orders/{order_number}", active_order_data)
                
                if completed_success:
                    # Remove from active orders
                    delete_data(f"active_orders/{order_number}")
                    delete_data(f"order_status/{str(user_id)}")
                    
                    # Clean up all temporary business data
                    cleanup_business_data_after_order_completion(order_number, user_id)
                    
                    logger.info(f"Completed order {order_number} and cleaned up temporary data")
                    return True
                else:
                    logger.error(f"Failed to store completed order {order_number}")
                    return False
            else:
                logger.warning(f"Order {order_number} not found in active orders")
                return False
                
        except Exception as e:
            logger.error(f"Error completing order {order_number}: {e}")
            return False
    
    def cancel_order(self, order_number: str, user_id: int, reason: str = "") -> bool:
        """
        Cancel an order and clean up all temporary data.
        
        Args:
            order_number: Order number to cancel
            user_id: User ID associated with the order
            reason: Cancellation reason
            
        Returns:
            True if order cancelled successfully
        """
        try:
            # Get order data
            active_order_data = get_data(f"active_orders/{order_number}")
            
            if active_order_data:
                # Add cancellation info
                active_order_data['cancelled_at'] = datetime.now().isoformat()
                active_order_data['status'] = 'CANCELLED'
                active_order_data['cancellation_reason'] = reason
                
                # Store in cancelled orders
                cancelled_success = set_data(f"cancelled_orders/{order_number}", active_order_data)
                
                if cancelled_success:
                    # Remove from active orders
                    delete_data(f"active_orders/{order_number}")
                    delete_data(f"order_status/{str(user_id)}")
                    
                    # Clean up all temporary business data
                    cleanup_business_data_after_order_completion(order_number, user_id)
                    
                    logger.info(f"Cancelled order {order_number} and cleaned up temporary data")
                    return True
                else:
                    logger.error(f"Failed to store cancelled order {order_number}")
                    return False
            else:
                logger.warning(f"Order {order_number} not found in active orders")
                return False
                
        except Exception as e:
            logger.error(f"Error cancelling order {order_number}: {e}")
            return False
    
    # ========================================================================
    # ADMIN REVIEW MANAGEMENT
    # ========================================================================
    
    def add_pending_admin_review(self, order_number: str, review_data: Dict[str, Any]) -> bool:
        """
        Add order for admin review in Firebase.
        
        Args:
            order_number: Order number
            review_data: Review data
            
        Returns:
            True if added successfully
        """
        try:
            firebase_path = f"pending_admin_reviews/{order_number}"
            firebase_success = set_data(firebase_path, review_data)
            
            if firebase_success:
                # Cache temporarily
                from src.data_models import temp_pending_admin_reviews
                temp_pending_admin_reviews[order_number] = review_data
                
                logger.info(f"Added order {order_number} for admin review")
                return True
            else:
                logger.error(f"Failed to add order {order_number} for admin review")
                return False
                
        except Exception as e:
            logger.error(f"Error adding admin review: {e}")
            return False
    
    def remove_pending_admin_review(self, order_number: str) -> bool:
        """
        Remove order from pending admin reviews.
        
        Args:
            order_number: Order number to remove
            
        Returns:
            True if removed successfully
        """
        try:
            # Remove from Firebase
            firebase_success = delete_data(f"pending_admin_reviews/{order_number}")
            
            if firebase_success:
                # Remove from cache
                from src.data_models import temp_pending_admin_reviews
                if order_number in temp_pending_admin_reviews:
                    del temp_pending_admin_reviews[order_number]
                
                logger.info(f"Removed order {order_number} from pending admin reviews")
                return True
            else:
                logger.error(f"Failed to remove order {order_number} from admin reviews")
                return False
                
        except Exception as e:
            logger.error(f"Error removing admin review: {e}")
            return False
    
    # ========================================================================
    # USER SESSION MANAGEMENT
    # ========================================================================
    
    def cleanup_user_session(self, user_id: int) -> bool:
        """
        Clean up user session data when session expires.
        
        Args:
            user_id: User ID to clean up
            
        Returns:
            True if cleanup successful
        """
        try:
            # Clean up temporary session data
            cleanup_user_session_data(user_id)
            
            # Remove any active order status
            delete_data(f"order_status/{str(user_id)}")
            
            logger.info(f"Cleaned up session data for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error cleaning up user session: {e}")
            return False


# Global instance
firebase_business_data = FirebaseBusinessDataManager()


def get_firebase_business_data_manager() -> FirebaseBusinessDataManager:
    """Get the global Firebase business data manager instance."""
    return firebase_business_data
