"""
Data synchronization utilities for Wiz Aroma Food Delivery system.
Ensures data consistency between Firebase collections and resolves ID mapping issues.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from src.firebase_db import get_data, set_data
from src.config import logger

def validate_data_consistency() -> Dict[str, Any]:
    """
    Validate data consistency across Firebase collections.
    Returns a report of inconsistencies and missing data.
    """
    logger.info("🔍 Starting data consistency validation...")
    
    report = {
        "areas": {"count": 0, "issues": []},
        "restaurants": {"count": 0, "issues": []},
        "delivery_locations": {"count": 0, "issues": []},
        "delivery_fees": {"count": 0, "issues": []},
        "orphaned_fees": [],
        "missing_fees": [],
        "summary": {"total_issues": 0, "critical_issues": 0}
    }
    
    try:
        # Load all data from Firebase
        areas_data = get_data("areas") or {"areas": []}
        restaurants_data = get_data("restaurants") or {"restaurants": []}
        delivery_locations_data = get_data("delivery_locations") or {"delivery_locations": []}
        delivery_fees_data = get_data("delivery_fees") or {"delivery_fees": []}
        
        areas = areas_data.get("areas", [])
        restaurants = restaurants_data.get("restaurants", [])
        delivery_locations = delivery_locations_data.get("delivery_locations", [])
        delivery_fees = delivery_fees_data.get("delivery_fees", [])
        
        report["areas"]["count"] = len(areas)
        report["restaurants"]["count"] = len(restaurants)
        report["delivery_locations"]["count"] = len(delivery_locations)
        report["delivery_fees"]["count"] = len(delivery_fees)
        
        # Create ID mappings
        area_ids = {int(area["id"]) for area in areas if "id" in area}
        location_ids = {int(location["id"]) for location in delivery_locations if "id" in location}
        
        logger.info(f"Found area IDs: {sorted(area_ids)}")
        logger.info(f"Found location IDs: {sorted(location_ids)}")
        
        # Check delivery fees for orphaned references
        for fee in delivery_fees:
            try:
                fee_area_id = int(fee.get("area_id", 0))
                fee_location_id = int(fee.get("location_id", 0))
                
                if fee_area_id not in area_ids:
                    issue = f"Delivery fee references non-existent area_id {fee_area_id}"
                    report["orphaned_fees"].append({
                        "type": "orphaned_area",
                        "fee": fee,
                        "issue": issue
                    })
                    report["delivery_fees"]["issues"].append(issue)
                    report["summary"]["critical_issues"] += 1
                
                if fee_location_id not in location_ids:
                    issue = f"Delivery fee references non-existent location_id {fee_location_id}"
                    report["orphaned_fees"].append({
                        "type": "orphaned_location", 
                        "fee": fee,
                        "issue": issue
                    })
                    report["delivery_fees"]["issues"].append(issue)
                    report["summary"]["critical_issues"] += 1
                    
            except (ValueError, TypeError) as e:
                issue = f"Invalid delivery fee data: {fee} - {e}"
                report["delivery_fees"]["issues"].append(issue)
                report["summary"]["total_issues"] += 1
        
        # Check for missing delivery fees (areas/locations without fees)
        for area in areas:
            try:
                area_id = int(area["id"])
                for location in delivery_locations:
                    try:
                        location_id = int(location["id"])
                        
                        # Check if this area-location combination has a delivery fee
                        has_fee = any(
                            int(fee.get("area_id", 0)) == area_id and 
                            int(fee.get("location_id", 0)) == location_id
                            for fee in delivery_fees
                        )
                        
                        if not has_fee:
                            missing_fee = {
                                "area_id": area_id,
                                "area_name": area.get("name", "Unknown"),
                                "location_id": location_id,
                                "location_name": location.get("name", "Unknown")
                            }
                            report["missing_fees"].append(missing_fee)
                            
                    except (ValueError, TypeError):
                        continue
            except (ValueError, TypeError):
                continue
        
        # Calculate total issues
        report["summary"]["total_issues"] = (
            len(report["areas"]["issues"]) +
            len(report["restaurants"]["issues"]) +
            len(report["delivery_locations"]["issues"]) +
            len(report["delivery_fees"]["issues"])
        )
        
        logger.info(f"Data validation complete. Found {report['summary']['total_issues']} total issues, {report['summary']['critical_issues']} critical issues")
        
        return report
        
    except Exception as e:
        logger.error(f"Error during data validation: {e}")
        report["summary"]["validation_error"] = str(e)
        return report

def get_valid_delivery_locations_for_area(area_id: int) -> List[Dict[str, Any]]:
    """
    Get delivery locations that have valid delivery fees for the specified area.
    Only returns locations that actually exist in Firebase with valid fees.
    """
    try:
        area_id = int(area_id)
        
        # Load data from Firebase
        delivery_locations_data = get_data("delivery_locations") or {"delivery_locations": []}
        delivery_fees_data = get_data("delivery_fees") or {"delivery_fees": []}
        
        delivery_locations = delivery_locations_data.get("delivery_locations", [])
        delivery_fees = delivery_fees_data.get("delivery_fees", [])
        
        valid_locations = []
        
        for location in delivery_locations:
            try:
                location_id = int(location["id"])
                location_name = location["name"]
                
                # Check if this location has a valid delivery fee for this area
                fee = None
                for delivery_fee in delivery_fees:
                    try:
                        if (int(delivery_fee.get("area_id", 0)) == area_id and 
                            int(delivery_fee.get("location_id", 0)) == location_id):
                            fee = delivery_fee.get("fee", 0)
                            break
                    except (ValueError, TypeError):
                        continue
                
                if fee and fee > 0:
                    valid_locations.append({
                        "id": location_id,
                        "name": location_name,
                        "fee": fee
                    })
                    logger.debug(f"Valid location found: {location_name} (ID: {location_id}) with fee {fee} for area {area_id}")
                else:
                    logger.debug(f"Skipping location {location_name} (ID: {location_id}) - no valid fee for area {area_id}")
                    
            except (ValueError, TypeError) as e:
                logger.error(f"Error processing location {location}: {e}")
                continue
        
        logger.info(f"Found {len(valid_locations)} valid delivery locations for area {area_id}")
        return valid_locations
        
    except Exception as e:
        logger.error(f"Error getting valid delivery locations for area {area_id}: {e}")
        return []

def sync_delivery_fees_with_locations() -> bool:
    """
    Synchronize delivery fees to ensure all area-location combinations have fees.
    This function can be used to fix missing delivery fees.
    """
    try:
        logger.info("🔄 Starting delivery fees synchronization...")
        
        # Get validation report first
        report = validate_data_consistency()
        missing_fees = report.get("missing_fees", [])
        
        if not missing_fees:
            logger.info("✅ No missing delivery fees found. All area-location combinations have fees.")
            return True
        
        logger.warning(f"Found {len(missing_fees)} missing delivery fee combinations")
        
        # For now, just log the missing fees - actual fee creation should be done via maintenance bot
        for missing in missing_fees:
            logger.warning(
                f"Missing delivery fee: {missing['area_name']} (ID: {missing['area_id']}) -> "
                f"{missing['location_name']} (ID: {missing['location_id']})"
            )
        
        logger.info("Delivery fees synchronization complete. Use maintenance bot to add missing fees.")
        return True
        
    except Exception as e:
        logger.error(f"Error during delivery fees synchronization: {e}")
        return False

def cleanup_orphaned_delivery_fees() -> bool:
    """
    Remove delivery fees that reference non-existent areas or locations.
    """
    try:
        logger.info("🧹 Starting orphaned delivery fees cleanup...")
        
        # Get validation report
        report = validate_data_consistency()
        orphaned_fees = report.get("orphaned_fees", [])
        
        if not orphaned_fees:
            logger.info("✅ No orphaned delivery fees found.")
            return True
        
        logger.warning(f"Found {len(orphaned_fees)} orphaned delivery fees")
        
        # Load current delivery fees
        delivery_fees_data = get_data("delivery_fees") or {"delivery_fees": []}
        current_fees = delivery_fees_data.get("delivery_fees", [])
        
        # Remove orphaned fees
        cleaned_fees = []
        removed_count = 0
        
        for fee in current_fees:
            is_orphaned = any(
                orphaned["fee"] == fee for orphaned in orphaned_fees
            )
            
            if not is_orphaned:
                cleaned_fees.append(fee)
            else:
                logger.info(f"Removing orphaned delivery fee: {fee}")
                removed_count += 1
        
        # Save cleaned fees back to Firebase
        if removed_count > 0:
            delivery_fees_data["delivery_fees"] = cleaned_fees
            if set_data("delivery_fees", delivery_fees_data):
                logger.info(f"✅ Successfully removed {removed_count} orphaned delivery fees")
                return True
            else:
                logger.error("❌ Failed to save cleaned delivery fees to Firebase")
                return False
        else:
            logger.info("✅ No orphaned delivery fees to remove")
            return True
        
    except Exception as e:
        logger.error(f"Error during orphaned delivery fees cleanup: {e}")
        return False
