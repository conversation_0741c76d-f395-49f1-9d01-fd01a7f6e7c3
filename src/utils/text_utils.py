"""
Author: <PERSON><PERSON><PERSON><PERSON> Nigatu <EMAIL>
Date: 2025-04-30 11:10:40
LastEditors: <PERSON><PERSON><PERSON><PERSON> Nigatu <EMAIL>
LastEditTime: 2025-04-30 21:19:40
FilePath: \\WA-Adama-V-1.3\\src\\utils\\text_utils.py
Description: Text utility functions for the Wiz Aroma Food Delivery system
"""

"""
Text utility functions for the Wiz Aroma Food Delivery system.
"""


def escape_markdown(text):
    """Escape Markdown special characters to prevent formatting issues"""
    if not text:
        return ""

    # Characters that need escaping in Markdown
    # NOTE: Removed '.' from special_chars to fix decimal number display
    special_chars = [
        "_",
        "*",
        "[",
        "]",
        "(",
        ")",
        "~",
        "`",
        ">",
        "#",
        "+",
        "-",
        "=",
        "|",
        "{",
        "}",
        "!",
    ]

    # Escape each special character
    for char in special_chars:
        text = text.replace(char, f"\\{char}")

    return text
