"""
Order handlers for the Wiz Aroma Delivery Bot.
Contains handlers for the order flow.
"""

import datetime
from telebot import types

from src.bot_instance import bot
from src.utils.keyboards import (
    get_main_menu_markup,
    get_restaurants_markup,
    get_menu_items_markup,
    get_delivery_gates_markup,
    get_delivery_name_markup,
    get_phone_number_markup,
    get_order_confirmation_markup,
    get_areas_markup,
)
from src.data_models import (
    orders,
    order_status,
    user_names,
    user_phone_numbers,
    current_order_numbers,
)
from src.data_storage import (
    save_user_data,
    clean_up_order_data,
    get_all_areas,
    get_all_restaurants,
    get_restaurant_by_id,
    get_restaurant_menu,
    get_area_by_id,
    get_restaurants_by_area,
    update_points_balance,
    get_area_id_by_name,
    set_current_order_area,
    get_all_delivery_locations,
    get_delivery_fee,
    get_delivery_location_by_id,
)
from src.utils.time_utils import is_open_now, generate_order_number
from src.utils.validation import is_valid_phone
from src.utils.text_utils import escape_markdown
from src.handlers.admin_handlers import send_order_for_review
from src.config import logger
from src.firebase_db import get_user_names, get_user_phone_numbers


@bot.message_handler(
    func=lambda message: message.text and message.text == "🍽️ Order Food"
)
def handle_area_selection(message):
    """Show areas for food delivery"""
    try:
        # Check if the bot is currently open for orders
        if not is_open_now():
            markup = get_main_menu_markup()

            bot.send_message(
                message.chat.id,
                "⏰ Sorry, we are currently closed for orders!\n\n"
                "Our working hours are:\n"
                "• Lunch Service: 5:30 AM - 7:30 AM\n"
                "• Dinner Service: 10:30 PM - 2:00 AM\n"
                "• Available all days of the week\n"
                "(Ethiopian Local Time)\n\n"
                "Please come back during our operating hours. Thank you!",
                reply_markup=markup,
            )
            return

        # Get markup with areas from data storage
        markup = get_areas_markup()

        bot.send_message(
            message.chat.id,
            "📍 Please select your area for delivery:",
            reply_markup=markup,
        )
    except Exception as e:
        logger.error(f"Error in handle_area_selection: {e}")
        bot.send_message(message.chat.id, "❌ Error loading areas. Please try again.")


@bot.message_handler(
    func=lambda message: message.text and message.text.startswith("📍 ")
)
def handle_area_restaurants(message):
    """Handle restaurants for selected area"""
    try:
        user_id = message.from_user.id
        area_name = message.text.replace("📍 ", "")

        logger.info(f"User {user_id} selected area: {area_name}")

        # Try to find area in dynamic data first
        areas = get_all_areas()
        area_id = None

        # Find area id by name in dynamic data
        for area in areas:
            if area["name"] == area_name:
                area_id = area["id"]
                break

        # If area found in dynamic data, get restaurants from data storage
        if area_id:
            # Get restaurants for this area from data storage
            restaurants_list = get_restaurants_by_area(area_id)

            if restaurants_list:
                logger.info(
                    f"Found {len(restaurants_list)} restaurants in area {area_name} from data storage"
                )
                # Show restaurants markup using the restaurants list
                bot.send_message(
                    user_id,
                    f"Please select a restaurant in {area_name}:",
                    reply_markup=get_restaurants_markup(restaurants_list),
                )
                # Store area information in user's current order
                set_current_order_area(user_id, area_name, area_id)
                return
            else:
                logger.warning(
                    f"No restaurants found in data storage for area {area_name} (id: {area_id})"
                )

        # Fallback to static config if needed
        from src.config import restaurants as config_restaurants

        if area_name in config_restaurants:
            logger.info(f"Using static config for area {area_name}")
            # Show restaurants markup using area name
            bot.send_message(
                user_id,
                f"Please select a restaurant in {area_name}:",
                reply_markup=get_restaurants_markup(area_name),
            )
            # Store area information in user's current order
            # For static config, we need to map area_name to an ID
            set_current_order_area(
                user_id, area_name, area_id or get_area_id_by_name(area_name)
            )
        else:
            logger.warning(f"Area {area_name} not found in static config")
            bot.send_message(
                user_id,
                "I couldn't find any restaurants in that area. Please try another area.",
                reply_markup=get_areas_markup(),
            )
    except Exception as e:
        logger.error(f"Error in handle_area_restaurants: {e}")
        bot.send_message(
            message.from_user.id,
            "Sorry, I encountered an error showing restaurants. Please try again.",
            reply_markup=get_areas_markup(),
        )


@bot.message_handler(
    func=lambda message: message.text and message.text.startswith("🏪 ")
)
def handle_restaurant_selection(message):
    """Handle restaurant selection"""
    try:
        # Check if the bot is currently open for orders
        if not is_open_now():
            markup = get_main_menu_markup()

            bot.send_message(
                message.chat.id,
                "⏰ Sorry, we are currently closed for orders!\n\n"
                "Our working hours are:\n"
                "• Lunch Service: 5:30 AM - 7:30 AM\n"
                "• Dinner Service: 10:30 PM - 2:00 AM\n"
                "• Available all days of the week\n"
                "(Ethiopian Local Time)\n\n"
                "Please come back during our operating hours. Thank you!",
                reply_markup=markup,
            )
            return

        user_id = message.from_user.id
        restaurant_name = message.text.replace("🏪 ", "")
        logger.info(f"User {user_id} is selecting restaurant: {restaurant_name}")

        # Get fresh restaurants data
        all_restaurants = get_all_restaurants()
        logger.debug(f"Found {len(all_restaurants)} restaurants in database")

        # Find restaurant by name
        restaurant_id = None
        restaurant_area_id = None

        for restaurant in all_restaurants:
            if restaurant["name"] == restaurant_name:
                restaurant_id = restaurant["id"]
                restaurant_area_id = restaurant["area_id"]
                logger.info(
                    f"Found restaurant ID {restaurant_id} in area {restaurant_area_id}"
                )
                break

        if not restaurant_id:
            logger.warning(f"Restaurant not found: {restaurant_name}")
            bot.send_message(message.chat.id, "❌ Restaurant not found.")
            return

        # Get area info for the restaurant
        area = get_area_by_id(restaurant_area_id)
        area_name = area["name"] if area else "Unknown Area"
        logger.info(f"Restaurant {restaurant_name} is in area: {area_name}")

        # Initialize or reset user's order
        orders[user_id] = {
            "restaurant": restaurant_name,
            "restaurant_id": restaurant_id,
            "restaurant_area": area_name,
            "items": [],
        }
        logger.debug(f"Initialized order data for user {user_id}")

        # Show restaurant menu - load directly from storage
        try:
            # Ensure restaurant_id is properly treated as an integer when needed
            logger.debug(f"Attempting to load menu for restaurant ID {restaurant_id}")
            menu = get_restaurant_menu(restaurant_id)

            if menu:
                logger.info(
                    f"Loaded menu with {len(menu)} items for restaurant {restaurant_id}"
                )
                markup = get_menu_items_markup(restaurant_id, menu)

                menu_text = (
                    f"📋 Menu for {restaurant_name}\n\n"
                    "Please select items from the keyboard menu below (max 5 items per order).\n\n"
                    "When you're done, click '✅ Complete Order'."
                )

                bot.send_message(message.chat.id, menu_text, reply_markup=markup)
                order_status[user_id] = "SELECTING_ITEMS"
            else:
                logger.warning(f"No menu items found for restaurant {restaurant_id}")
                bot.send_message(
                    message.chat.id,
                    "❌ Menu not available for this restaurant. Please try another restaurant or contact support.",
                )
        except Exception as menu_error:
            logger.error(
                f"Error loading menu for restaurant {restaurant_id}: {menu_error}",
                exc_info=True,
            )
            bot.send_message(
                message.chat.id,
                "❌ There was an error loading the menu. Please try another restaurant or contact support.",
            )

    except Exception as e:
        logger.error(f"Error in handle_restaurant_selection: {e}", exc_info=True)
        bot.send_message(
            message.chat.id, "❌ Error selecting restaurant. Please try again."
        )


@bot.message_handler(
    func=lambda message: message.text and message.text.startswith("➕ ")
)
def add_menu_item(message):
    """Add a menu item to the order"""
    try:
        user_id = message.from_user.id
        if user_id in orders:
            # Extract item name and price from message
            menu_text = message.text.replace("➕ ", "")

            # Format could be either "Item Name (Price birr)" or "Item Name (Size) (Price birr)"
            if menu_text.count("(") > 1:
                # Handle format with size: "Item Name (Size) (Price birr)"
                item_name = menu_text.rsplit(" (", 1)[0].strip()
                # Extract just the price part from the last parenthesis
                price_part = menu_text.rsplit(" (", 1)[1]
                item_price = int(price_part.split(" birr)")[0].strip())
            else:
                # Handle standard format: "Item Name (Price birr)"
                item_name = menu_text.split(" (")[0].strip()
                item_price = int(menu_text.split("(")[1].split(" birr)")[0].strip())

            # Get restaurant info and menu
            restaurant_id = orders[user_id]["restaurant_id"]
            restaurant_menu = get_restaurant_menu(restaurant_id)

            # Check if we can add another item (max 5)
            if len(orders[user_id]["items"]) >= 5:
                bot.send_message(
                    message.chat.id,
                    "❌ You've reached the maximum of 5 items per order. Please complete your order or cancel to start a new one.",
                )
                return

            # Find the item in the menu
            found_item = None
            for menu_item in restaurant_menu:
                if menu_item["name"] == item_name and menu_item["price"] == item_price:
                    found_item = menu_item
                    break

            if found_item:
                # Add the item to the order
                orders[user_id]["items"].append(
                    {
                        "id": found_item["id"],
                        "name": found_item["name"],
                        "price": found_item["price"],
                    }
                )

                # Update total price
                if "total_price" not in orders[user_id]:
                    orders[user_id]["total_price"] = 0
                orders[user_id]["total_price"] += found_item["price"]

                # Show order summary after adding item
                items_count = len(orders[user_id]["items"])
                remaining_items = 5 - items_count
                bot.send_message(
                    message.chat.id,
                    f"✅ Added {item_name} to your order!\n"
                    f"🛍️ Cart: {items_count} item(s)\n"
                    f"📝 You can add {remaining_items} more item(s)\n"
                    f"💰 Total: {orders[user_id]['total_price']} birr",
                )
            else:
                bot.send_message(message.chat.id, "❌ Item not found in the menu.")
        else:
            bot.send_message(message.chat.id, "❌ Please select a restaurant first.")
    except Exception as e:
        logger.error(f"Error in add_menu_item: {e}")
        bot.send_message(
            message.chat.id, "❌ Error adding item to order. Please try again."
        )


@bot.message_handler(func=lambda message: message.text == "✅ Complete Order")
def handle_order_confirmation(message):
    """Handle order completion"""
    try:
        user_id = message.from_user.id
        if user_id not in orders:
            bot.send_message(message.chat.id, "No active order found.")
            return

        # Check if there are items in the cart
        if not orders[user_id].get("items", []):
            bot.send_message(
                message.chat.id,
                "❌ Your cart is empty. Please add some items before completing your order.",
            )
            return

        # Check if this is a menu update (user already has order details)
        if (
            "order_description" in orders[user_id]
            and "delivery_gate" in orders[user_id]
            and "delivery_name" in orders[user_id]
            and "phone_number" in orders[user_id]
        ):

            # Generate order number if not already present
            if "order_number" not in orders[user_id]:
                order_number = generate_order_number(user_id)
                current_order_numbers[user_id] = order_number
                orders[user_id]["order_number"] = order_number

            # Skip to order summary
            generate_and_send_order_summary(message.chat.id, user_id)
            return

        # First ask for order description
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("⏩ Skip Description"),
            types.KeyboardButton("❌ Cancel Order"),
        )

        bot.send_message(
            message.chat.id,
            "📝 Please enter any special instructions or notes for your order:\n\n"
            "(e.g., 'Alicha 🟡', 'with berbere 🔴', 'Additional Extras')",
            reply_markup=markup,
        )
        order_status[user_id] = "AWAITING_ORDER_DESCRIPTION"

    except Exception as e:
        logger.error(f"Error in handle_order_confirmation: {str(e)}")
        bot.send_message(message.chat.id, "Error completing order. Please try again.")


@bot.message_handler(
    func=lambda message: order_status.get(message.from_user.id)
    == "AWAITING_ORDER_DESCRIPTION"
)
def handle_order_description(message):
    """Handle order description"""
    try:
        user_id = message.from_user.id

        if message.text == "❌ Cancel Order":
            clean_up_order_data(user_id, None)
            # Create markup with Order and Main Menu buttons
            markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
            markup.add(
                types.KeyboardButton("🍽️ Order Food"),
                types.KeyboardButton("🔙 Back to Main Menu"),
                types.KeyboardButton("💫 My Points"),
                types.KeyboardButton("ℹ️ Help"),
            )
            bot.reply_to(
                message,
                "Order cancelled. You can start a new order anytime!",
                reply_markup=markup,
            )
            return

        # Check if user wants to skip description
        if message.text == "⏩ Skip Description":
            description = "No special instructions"
        else:
            description = message.text.strip()

        # Save the description in the order
        orders[user_id]["order_description"] = description

        # Now ask for delivery location
        # Get the restaurant area ID
        try:
            restaurant_id = int(orders[user_id]["restaurant_id"])
            logger.info(f"Restaurant ID: {restaurant_id}, Type: {type(restaurant_id)}")

            # Update stored ID to ensure it's an integer
            orders[user_id]["restaurant_id"] = restaurant_id
        except (ValueError, TypeError) as e:
            logger.error(f"Failed to convert restaurant_id to integer: {e}")
            restaurant_id = orders[user_id]["restaurant_id"]
            logger.info(
                f"Using original restaurant_id: {restaurant_id}, Type: {type(restaurant_id)}"
            )

        restaurant = get_restaurant_by_id(restaurant_id)

        if restaurant is None:
            logger.error(f"Restaurant not found with ID: {restaurant_id}")
            bot.send_message(
                message.chat.id,
                "❌ Error: Restaurant not found. Please try again.",
            )
            return

        logger.info(f"Restaurant found: {restaurant}")

        try:
            restaurant_area_id = int(restaurant["area_id"])
            logger.info(
                f"Restaurant area ID: {restaurant_area_id}, Type: {type(restaurant_area_id)}"
            )
        except (ValueError, TypeError) as e:
            logger.error(f"Failed to convert restaurant_area_id to integer: {e}")
            restaurant_area_id = restaurant["area_id"]
            logger.info(
                f"Using original restaurant_area_id: {restaurant_area_id}, Type: {type(restaurant_area_id)}"
            )

        # Get the area name
        area = get_area_by_id(restaurant_area_id)
        logger.info(f"Area found: {area}")
        area_name = area["name"] if area else "Unknown Area"

        # Store the area ID in the order for later use
        orders[user_id]["restaurant_area_id"] = restaurant_area_id
        logger.info(
            f"Stored restaurant_area_id in order: {orders[user_id]['restaurant_area_id']}"
        )

        # Get delivery gates markup using area name and area ID
        markup = get_delivery_gates_markup(area_name, restaurant_area_id)

        bot.send_message(
            message.chat.id,
            "Please select your delivery location:",
            reply_markup=markup,
        )
        order_status[user_id] = "AWAITING_DELIVERY_LOCATION"

    except Exception as e:
        logger.error(f"Error in handle_order_description: {e}")
        bot.send_message(
            message.chat.id,
            "❌ Error processing your order description. Please try again.",
        )


@bot.message_handler(
    func=lambda message: message.text and message.text.startswith("🚪 ")
)
def handle_delivery_gate(message):
    """Handle delivery gate selection"""
    try:
        user_id = message.from_user.id

        logger.info(f"User {user_id} selected delivery gate: {message.text}")

        # Extract gate name from button text - handle complex names with parentheses
        gate_text = message.text.replace("🚪 ", "")

        # Find the last occurrence of " (" followed by a number and "birr)" to extract the fee
        # This handles cases like "Masters Dorm (Lebs Matebiya) (35 birr)"
        import re
        fee_pattern = r'\s+\(\d+\s+birr\)$'
        match = re.search(fee_pattern, gate_text)

        if match:
            # Remove the fee part to get the location name
            location_name = gate_text[:match.start()].strip()
        else:
            # Fallback to old method if pattern doesn't match
            location_name = gate_text.split(" (")[0] if " (" in gate_text else gate_text

        logger.info(f"Extracted location name: '{location_name}' from button text: '{gate_text}'")

        # Get the restaurant area ID from the current order
        restaurant_area_id = orders.get(user_id, {}).get("restaurant_area_id")
        if not restaurant_area_id:
            logger.error(f"No restaurant_area_id found in order for user {user_id}")
            bot.send_message(
                message.chat.id, "❌ Error: Restaurant area not found. Please restart your order."
            )
            return

        # Find the delivery location ID by matching the location name
        location_id = None
        for location in get_all_delivery_locations():
            if location["name"] == location_name:
                location_id = location["id"]
                break

        if not location_id:
            logger.error(f"No location ID found for location name: {location_name}")
            bot.send_message(
                message.chat.id, "❌ Error: Delivery location not found. Please try again."
            )
            return

        # Retrieve the delivery fee from Firebase using area_id and location_id
        try:
            fee = get_delivery_fee(restaurant_area_id, location_id)
            logger.info(f"Retrieved delivery fee from Firebase: area_id={restaurant_area_id}, location_id={location_id}, fee={fee}")
        except Exception as e:
            logger.error(f"Error retrieving delivery fee from Firebase: {e}")
            fee = 0
            logger.warning(f"Using fallback delivery fee of 0 for area_id={restaurant_area_id}, location_id={location_id}")

        # Validate that we have a valid delivery fee
        if fee <= 0:
            logger.warning(f"Invalid delivery fee ({fee}) for area_id={restaurant_area_id}, location_id={location_id}")
            # Try to get fee from legacy config as fallback
            from src.config import delivery_fees
            area = get_area_by_id(restaurant_area_id)
            area_name = area["name"] if area else None
            if area_name and area_name in delivery_fees and location_name in delivery_fees[area_name]:
                fee = delivery_fees[area_name][location_name]
                logger.info(f"Using legacy delivery fee: {fee} for {area_name} -> {location_name}")
            else:
                logger.error(f"No delivery fee found in Firebase or legacy config for {area_name} -> {location_name}")

        # Store delivery gate info
        orders[user_id]["delivery_gate"] = location_name
        orders[user_id]["delivery_location_id"] = location_id
        orders[user_id]["delivery_fee"] = fee

        logger.info(
            f"Successfully stored delivery info: gate={location_name}, id={location_id}, fee={fee}"
        )

        # Make sure we load the latest user data
        names_data = get_user_names()
        if str(user_id) in names_data:
            user_names[user_id] = names_data[str(user_id)]

        # Now ask for delivery name
        saved_name = user_names.get(user_id)
        logger.info(f"Using saved name for user {user_id}: {saved_name}")
        markup = get_delivery_name_markup(saved_name)

        bot.send_message(
            message.chat.id, "Please enter Your name:", reply_markup=markup
        )
        order_status[user_id] = "AWAITING_DELIVERY_NAME"

    except Exception as e:
        logger.error(f"Error in handle_delivery_gate: {str(e)}")
        bot.send_message(
            message.chat.id, "❌ Error processing delivery location. Please try again."
        )


@bot.message_handler(
    func=lambda message: order_status.get(message.from_user.id)
    == "AWAITING_DELIVERY_NAME"
)
def handle_delivery_name(message):
    """Handle delivery name input"""
    user_id = message.from_user.id

    if message.text == "❌ Cancel Order":
        clean_up_order_data(user_id, None)
        # Create markup with Order and Main Menu buttons
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("🍽️ Order Food"),
            types.KeyboardButton("🔙 Back to Main Menu"),
            types.KeyboardButton("💫 My Points"),
            types.KeyboardButton("ℹ️ Help"),
        )
        bot.reply_to(
            message,
            "Order cancelled. You can start a new order anytime!",
            reply_markup=markup,
        )
        return

    # Handle saved name selection
    if message.text.startswith("📝 Use saved name:"):
        # Extract the name from the button text if needed, otherwise use stored value
        if user_id in user_names:
            delivery_name = user_names[user_id]
        else:
            # Extract name from button text as fallback
            delivery_name = message.text.replace("📝 Use saved name:", "").strip()
            user_names[user_id] = delivery_name
    else:
        delivery_name = message.text.strip()
        user_names[user_id] = delivery_name  # Save for future use

    # Save user data to ensure persistence
    save_user_data()

    orders[user_id]["delivery_name"] = delivery_name

    # Make sure we load the latest user phone number from Firebase
    from src.firebase_db import get_user_phone_numbers

    # Try to get user's saved phone from Firebase first
    phone_numbers_data = get_user_phone_numbers()
    if str(user_id) in phone_numbers_data:
        user_phone_numbers[user_id] = phone_numbers_data[str(user_id)]

    # Now ask for phone number
    saved_phone = user_phone_numbers.get(user_id)
    logger.info(f"Using saved phone for user {user_id}: {saved_phone}")
    markup = get_phone_number_markup(saved_phone)

    bot.send_message(
        message.chat.id, "Please enter your phone number:", reply_markup=markup
    )
    order_status[user_id] = "AWAITING_PHONE_NUMBER"


@bot.message_handler(
    func=lambda message: order_status.get(message.from_user.id)
    == "AWAITING_PHONE_NUMBER"
)
def handle_phone_number(message):
    """Handle phone number input"""
    user_id = message.from_user.id

    try:
        # Handle cancel request
        if message.text == "❌ Cancel Order":
            clean_up_order_data(user_id, None)
            # Create markup with Order and Main Menu buttons
            markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
            markup.add(
                types.KeyboardButton("🍽️ Order Food"),
                types.KeyboardButton("🔙 Back to Main Menu"),
                types.KeyboardButton("💫 My Points"),
                types.KeyboardButton("ℹ️ Help"),
            )
            bot.reply_to(
                message,
                "Order cancelled. You can start a new order anytime!",
                reply_markup=markup,
            )
            return

        # Get phone number from message
        phone_number = message.text.strip()

        # Check if the user selected "Use saved number"
        if phone_number.startswith("📱 Use saved number:"):
            # Extract the number from the button text if needed, otherwise use stored value
            if user_id in user_phone_numbers:
                phone_number = user_phone_numbers[user_id]
            else:
                # Extract number from button text as fallback
                phone_number = phone_number.replace("📱 Use saved number:", "").strip()
                user_phone_numbers[user_id] = phone_number

        # Validate phone number
        if not is_valid_phone(phone_number):
            bot.send_message(
                message.chat.id,
                "❌ Invalid phone number format. Please enter a valid phone number.",
            )
            return

        # Save the phone number in the order and persistently
        user_phone_numbers[user_id] = phone_number
        orders[user_id]["phone_number"] = phone_number

        # Save user data to ensure persistence
        save_user_data()

        # Generate order number directly instead of asking for email
        order_number = generate_order_number(user_id)
        current_order_numbers[user_id] = order_number
        orders[user_id]["order_number"] = order_number  # Store in order data

        # Store Telegram username in the order
        telegram_username = message.from_user.username or "Not provided"
        orders[user_id]["telegram_username"] = telegram_username

        # Generate and send order summary
        generate_and_send_order_summary(message.chat.id, user_id)

    except Exception as e:
        logger.error(f"Error in handle_phone_number: {e}")
        bot.send_message(
            message.chat.id, "❌ Error processing your phone number. Please try again."
        )


@bot.message_handler(
    func=lambda message: order_status.get(message.from_user.id)
    == "AWAITING_CONFIRMATION"
)
def handle_final_order_confirmation(message):
    """Handle final order confirmation"""
    try:
        user_id = message.from_user.id

        if message.text == "✅ Confirm Order":
            # Validate that user has an order
            if user_id not in orders:
                logger.error(f"User {user_id} trying to confirm order but no order found")
                bot.send_message(
                    message.chat.id,
                    "❌ No order found. Please start a new order.",
                )
                return

            order = orders[user_id]
            order_number = order.get("order_number")

            if not order_number:
                logger.error(f"Order for user {user_id} missing order number")
                bot.send_message(
                    message.chat.id,
                    "❌ Error with order number. Please try again.",
                )
                return

            # Add Telegram username to order data
            username = message.from_user.username
            order["username"] = username or "No username"

            try:
                # Regular order - send for admin review with enhanced error handling
                send_order_for_review(user_id, order)

                # Create markup with main menu option
                markup = types.ReplyKeyboardMarkup(row_width=1, resize_keyboard=True)
                markup.add(types.KeyboardButton("🔙 Back to Main Menu"))

                bot.send_message(
                    message.chat.id,
                    f"✅ Your order #{order_number} has been submitted for review!\n\n"
                    "Please wait for admin approval before proceeding to payment.\n"
                    "You will be notified once your order is approved.",
                    reply_markup=markup,
                )

                # Update order status
                order_status[user_id] = "AWAITING_ADMIN_APPROVAL"

                # Save order status to Firebase
                from src.firebase_db import update_order_status
                update_order_status(str(user_id), "AWAITING_ADMIN_APPROVAL")

                logger.info(f"Order #{order_number} successfully submitted for admin review by user {user_id}")

            except Exception as review_error:
                logger.error(f"Error sending order #{order_number} for review: {review_error}")
                bot.send_message(
                    message.chat.id,
                    "❌ Error processing your confirmation. Please try again.",
                )
                return

        elif message.text == "💾 Save as Favorite":
            # Import the prompt_for_favorite_name handler from favorite_orders_handlers
            from src.handlers.favorite_orders_handlers import prompt_for_favorite_name

            # Call the handler
            prompt_for_favorite_name(message)

        elif message.text == "🔄 Update Menu":
            # Handle update menu request
            handle_update_menu(message)

        elif message.text == "❌ Cancel Order":
            # Cancel order handling
            cancel_order(message)

        else:
            bot.send_message(
                message.chat.id,
                "Please confirm your order by selecting ✅ Confirm Order, 💾 Save as Favorite, 🔄 Update Menu, or ❌ Cancel Order.",
            )

    except Exception as e:
        logger.error(f"Error in handle_final_order_confirmation: {e}")
        bot.send_message(
            message.chat.id, "❌ Error processing your confirmation. Please try again."
        )


@bot.message_handler(func=lambda message: message.text == "❌ Cancel Order")
def cancel_order(message):
    """Handle order cancellation"""
    try:
        user_id = message.from_user.id

        # Check if user has an existing order
        if user_id not in orders:
            markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
            markup.add(
                types.KeyboardButton("🍽️ Order Food"),
                types.KeyboardButton("🔙 Back to Main Menu"),
            )
            bot.send_message(
                message.chat.id,
                "You don't have an active order to cancel.",
                reply_markup=markup,
            )
            return

        # Get order points info for restoring if needed
        points_used = orders[user_id].get("points_used", 0)

        # Restore points to user if they used points for this order
        if points_used > 0:
            # Import update_points_balance to restore points
            update_points_balance(user_id, points_used)
            logger.info(
                f"Restored {points_used} points to user {user_id} due to order cancellation"
            )

        # Get order number for log
        order_number = orders[user_id].get("order_number", "unknown")

        # Clean up all order data
        clean_up_order_data(user_id, order_number)

        # Return to main menu with Back to Main Menu button
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("🍽️ Order Food"),
            types.KeyboardButton("🔙 Back to Main Menu"),
            types.KeyboardButton("💫 My Points"),
            types.KeyboardButton("ℹ️ Help"),
        )

        bot.send_message(
            message.chat.id,
            "🚫 Order canceled. Your cart is now empty.",
            reply_markup=markup,
        )

        # Send additional confirmation that they can start over
        bot.send_message(
            message.chat.id,
            "You can start a new order whenever you're ready!",
        )
    except Exception as e:
        logger.error(f"Error in cancel_order: {e}")
        bot.send_message(message.chat.id, "❌ Error canceling order. Please try again.")


@bot.message_handler(func=lambda message: message.text == "🔙 Back to Areas")
def back_to_areas(message):
    """Return to the areas selection menu"""
    handle_area_selection(message)


@bot.message_handler(func=lambda message: message.text == "🔄 Update Menu")
def handle_update_menu(message):
    """Handle updating menu items while preserving user data"""
    try:
        user_id = message.from_user.id

        # Check if user has an existing order
        if user_id not in orders:
            bot.send_message(message.chat.id, "No active order found.")
            return

        # Get the restaurant ID from the current order
        restaurant_id = orders[user_id].get("restaurant_id")
        restaurant_name = orders[user_id].get("restaurant")

        if not restaurant_id:
            bot.send_message(
                message.chat.id, "❌ Error: Restaurant information not found."
            )
            return

        # Clear only the items in the order, keep other data
        orders[user_id]["items"] = []
        orders[user_id]["total_price"] = 0

        # Show restaurant menu again
        try:
            menu = get_restaurant_menu(restaurant_id)

            if menu:
                markup = get_menu_items_markup(restaurant_id, menu)

                menu_text = (
                    f"📋 Menu for {restaurant_name}\n\n"
                    "Please select items from the keyboard menu below (max 5 items per order).\n\n"
                    "When you're done, click '✅ Complete Order'."
                )

                bot.send_message(message.chat.id, menu_text, reply_markup=markup)
                order_status[user_id] = "SELECTING_ITEMS"
            else:
                bot.send_message(
                    message.chat.id,
                    "❌ Menu not available for this restaurant. Please try another restaurant or contact support.",
                )
        except Exception as menu_error:
            logger.error(
                f"Error loading menu for restaurant {restaurant_id}: {menu_error}"
            )
            bot.send_message(
                message.chat.id,
                "❌ There was an error loading the menu. Please try again or contact support.",
            )
    except Exception as e:
        logger.error(f"Error in handle_update_menu: {e}")
        bot.send_message(message.chat.id, "❌ Error updating menu. Please try again.")


def generate_and_send_order_summary(chat_id, user_id):
    """Generate and send order summary to the user"""
    try:
        # Get the order data
        order = orders[user_id]
        order_number = order.get("order_number")

        # Format order items
        items = order.get("items", [])

        # Count identical items
        item_counts = {}
        for item in items:
            name = item["name"]
            price = item["price"]

            if name in item_counts:
                item_counts[name]["count"] += 1
                item_counts[name]["total"] += price
            else:
                item_counts[name] = {"count": 1, "price": price, "total": price}

        # Format the order details using consolidated format
        from src.utils.helpers import consolidate_order_items
        # Convert item_counts back to items format for consolidate_order_items
        items_for_consolidation = []
        for name, details in item_counts.items():
            for _ in range(details["count"]):
                items_for_consolidation.append({"name": name, "price": details["price"]})

        order_details = consolidate_order_items(items_for_consolidation)

        # Calculate total
        subtotal = sum(item["price"] for item in items)
        delivery_fee = order.get("delivery_fee", 0)

        # Validate delivery fee and log for debugging
        logger.info(f"Order summary for user {user_id}: subtotal={subtotal}, delivery_fee={delivery_fee}")
        if delivery_fee <= 0:
            logger.warning(f"Invalid delivery fee ({delivery_fee}) in order summary for user {user_id}")
            # Try to recalculate delivery fee from stored location data
            restaurant_area_id = order.get("restaurant_area_id")
            delivery_location_id = order.get("delivery_location_id")
            if restaurant_area_id and delivery_location_id:
                try:
                    recalculated_fee = get_delivery_fee(restaurant_area_id, delivery_location_id)
                    if recalculated_fee > 0:
                        delivery_fee = recalculated_fee
                        order["delivery_fee"] = delivery_fee  # Update the order data
                        logger.info(f"Recalculated delivery fee: {delivery_fee} for area_id={restaurant_area_id}, location_id={delivery_location_id}")
                    else:
                        logger.error(f"Failed to recalculate delivery fee for area_id={restaurant_area_id}, location_id={delivery_location_id}")
                except Exception as e:
                    logger.error(f"Error recalculating delivery fee: {e}")

        total_price = subtotal + delivery_fee

        # Get delivery location
        delivery_gate = order.get("delivery_gate", "Unknown")
        # Escape Markdown in delivery gate
        safe_delivery_gate = escape_markdown(delivery_gate)

        # Get restaurant name
        restaurant_name = order.get("restaurant", "Unknown")
        # Escape Markdown in restaurant name
        safe_restaurant_name = escape_markdown(restaurant_name)

        # Get area name
        restaurant_area_id = order.get("restaurant_area_id")
        area = get_area_by_id(restaurant_area_id)
        area_name = area["name"] if area else "Unknown Area"

        # Store area name for display
        order["restaurant_area"] = area_name

        # Store subtotal
        order["subtotal"] = subtotal

        # Get order description if available
        order_description = order.get("order_description", "No special instructions")
        # Escape Markdown in order description
        safe_order_description = escape_markdown(order_description)

        # Telegram username is stored in order but not used in summary

        # Escape Markdown in delivery name and phone number
        safe_delivery_name = escape_markdown(order["delivery_name"])
        safe_phone_number = escape_markdown(order["phone_number"])

        summary = (
            f"🧾 ORDER SUMMARY - #{order_number}\n\n"
            f"👤 Name: {safe_delivery_name}\n"
            f"📱 Phone: {safe_phone_number}\n"
            f"📍 Delivery to: {safe_delivery_gate}\n\n"
            f"🏪 Restaurant: {safe_restaurant_name}\n\n"
            f"📋 Items:\n{order_details}\n"
            f"📝 Special Instructions: {safe_order_description}\n\n"
            f"💰 Subtotal: {subtotal} birr\n"
            f"🚚 Delivery Fee: {delivery_fee} birr\n"
            f"💵 Total: {total_price} birr\n\n"
            "Please confirm your order to proceed."
        )

        # Create confirmation buttons
        markup = get_order_confirmation_markup()

        # Send the order summary to the user
        bot.send_message(chat_id, summary, reply_markup=markup, parse_mode="Markdown")

        # Update order status
        order_status[user_id] = "AWAITING_CONFIRMATION"

        return True
    except Exception as e:
        logger.error(f"Error in generate_and_send_order_summary: {e}")
        bot.send_message(
            chat_id, "❌ Error generating order summary. Please try again."
        )
        return False


@bot.callback_query_handler(func=lambda call: call.data.startswith('confirm_delivery_'))
def handle_delivery_confirmation(call):
    """Handle customer confirmation of order delivery"""
    try:
        user_id = call.from_user.id
        order_number = call.data.replace('confirm_delivery_', '')

        logger.info(f"🔔 Customer {user_id} attempting to confirm delivery of order {order_number}")

        # Get order data to verify this user can confirm this order
        from src.firebase_db import get_data, set_data
        confirmed_orders = get_data("confirmed_orders") or {}

        if order_number not in confirmed_orders:
            bot.answer_callback_query(call.id, "❌ Order not found")
            return

        order_data = confirmed_orders[order_number]

        # Extract customer user ID from order data or order number
        order_user_id = order_data.get('user_id')

        # If user_id not in order data, extract from order number format: {user_id}_{date}_{sequence}
        if not order_user_id:
            try:
                order_user_id = order_number.split('_')[0]
                logger.info(f"🔍 Extracted order user ID from order number: {order_user_id}")
            except (IndexError, ValueError):
                logger.error(f"❌ Could not extract user ID from order number: {order_number}")
                bot.answer_callback_query(call.id, "❌ Error processing order confirmation")
                return

        # Verify this user can confirm this order
        logger.info(f"🔍 Order ownership check: order_user_id={order_user_id} (type: {type(order_user_id)}), user_id={user_id} (type: {type(user_id)})")

        if str(order_user_id) != str(user_id):
            logger.warning(f"❌ User {user_id} attempted to confirm order {order_number} belonging to user {order_user_id}")
            bot.answer_callback_query(call.id, "❌ You can only confirm your own orders")
            return

        logger.info(f"✅ Order ownership verified: User {user_id} can confirm order {order_number}")

        # Verify order is in completed status before allowing confirmation
        if order_data.get('delivery_status') != 'completed':
            bot.answer_callback_query(call.id, "❌ Order has not been marked as completed by delivery personnel yet")
            return

        # Update order status to customer confirmed with enhanced issue resolution tracking
        current_timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        order_data['status'] = 'CUSTOMER_CONFIRMED'
        order_data['delivery_status'] = 'customer_confirmed'
        order_data['customer_confirmed_at'] = current_timestamp

        # Enhanced issue resolution tracking for final completion
        had_issue = order_data.get('issue_reported_at') or order_data.get('delivery_status') == 'delivery_issue'

        if had_issue:
            # Track final issue resolution
            if 'issue_resolution_history' not in order_data:
                order_data['issue_resolution_history'] = []

            order_data['issue_resolution_history'].append({
                'timestamp': current_timestamp,
                'action': 'issue_finally_resolved',
                'confirmed_by': user_id,
                'status': 'customer_confirmed',
                'description': 'Customer confirmed receipt after issue resolution - order fully completed'
            })

            # Update analytics status - order is now fully resolved and complete
            order_data['analytics_status'] = 'completed_after_issue_resolution'
            order_data['issue_finally_resolved_at'] = current_timestamp

            logger.info(f"✅ Issue fully resolved for order {order_number} - customer confirmed receipt")
        else:
            # Normal completion
            order_data['analytics_status'] = 'completed_normal'

        set_data(f"confirmed_orders/{order_number}", order_data)

        # Update the message to show confirmation (NO delivery personnel details for privacy)
        bot.edit_message_text(
            f"✅ **Order Confirmed!**\n\n{call.message.text}\n\n✅ **You have confirmed receipt of this order.**\n📅 **Confirmed at**: {order_data['customer_confirmed_at']}\n\n🎉 **Order process complete!** Thank you for using Wiz Aroma Hawassa University Branch!",
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown'
        )

        bot.answer_callback_query(call.id, "✅ Order confirmed! Thank you for using Wiz Aroma Hawassa University Branch!")

        # Notify order tracking bot and trigger completion workflow
        try:
            from src.bots.order_track_bot import process_customer_confirmation
            # Use the full confirmation workflow which includes analytics updates
            process_customer_confirmation(order_number, order_data)
        except Exception as e:
            logger.error(f"Failed to process customer confirmation workflow: {e}")

        logger.info(f"Customer {user_id} confirmed delivery of order {order_number}")

    except Exception as e:
        logger.error(f"Error in handle_delivery_confirmation: {e}")
        bot.answer_callback_query(call.id, "❌ Error confirming order")


@bot.callback_query_handler(func=lambda call: call.data.startswith('not_received_'))
def handle_order_not_received(call):
    """Handle customer reporting that order was not received"""
    logger.info(f"🔥 ORDER NOT RECEIVED CALLBACK TRIGGERED! Data: {call.data}")

    # Immediately acknowledge the callback to stop loading indicator
    bot.answer_callback_query(call.id, "🚨 Processing your report...")

    try:
        user_id = call.from_user.id
        order_number = call.data.replace('not_received_', '')

        logger.info(f"🚨 Customer {user_id} reporting order {order_number} not received")

        # Get order data to verify this user can report this order
        from src.firebase_db import get_data, set_data
        confirmed_orders = get_data("confirmed_orders") or {}

        if order_number not in confirmed_orders:
            bot.answer_callback_query(call.id, "❌ Order not found")
            return

        order_data = confirmed_orders[order_number]

        # Verify this user can report this order
        order_user_id = order_data.get('user_id')
        if not order_user_id:
            # Extract from order number if not in data
            order_user_id = order_number.split('_')[0]

        if str(order_user_id) != str(user_id):
            bot.answer_callback_query(call.id, "❌ You can only report your own orders")
            return

        # Update order status to indicate delivery issue with enhanced tracking
        current_timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        order_data['delivery_status'] = 'delivery_issue'
        order_data['issue_reported_at'] = current_timestamp
        order_data['issue_reported_by'] = user_id

        # Enhanced issue resolution tracking
        if 'issue_resolution_history' not in order_data:
            order_data['issue_resolution_history'] = []

        order_data['issue_resolution_history'].append({
            'timestamp': current_timestamp,
            'action': 'issue_reported',
            'reported_by': user_id,
            'status': 'delivery_issue',
            'description': 'Customer reported order not received'
        })

        # Track confirmation attempts for analytics
        order_data['confirmation_attempts'] = order_data.get('confirmation_attempts', 0) + 1
        order_data['last_confirmation_attempt'] = current_timestamp

        # Keep order in incomplete status for analytics until resolved
        order_data['analytics_status'] = 'incomplete_with_issue'

        set_data(f"confirmed_orders/{order_number}", order_data)

        # Update analytics counters for issue order
        try:
            from src.bots.management_bot import update_order_analytics_counters
            update_order_analytics_counters('issue', 'increment')
            logger.info(f"📊 Updated analytics counters: order {order_number} marked as issue")
        except Exception as analytics_error:
            logger.error(f"Failed to update analytics counters for issue order {order_number}: {analytics_error}")

        # Acknowledge to customer
        bot.answer_callback_query(call.id, "🚨 Issue reported! We're investigating.")

        # Update the message to show issue reported
        try:
            bot.edit_message_text(
                f"🚨 **Delivery Issue Reported**\n\n"
                f"📋 **Order #{order_number}**\n"
                f"⚠️ **Status:** Delivery issue reported\n"
                f"🕐 **Reported at:** {order_data['issue_reported_at']}\n\n"
                f"We're investigating this issue and will contact you shortly.\n"
                f"Thank you for your patience.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )
        except Exception as edit_error:
            logger.error(f"Failed to update message for delivery issue: {edit_error}")

        # Notify order tracking bot about the delivery issue
        try:
            from src.bots.order_track_bot import send_order_status_update
            send_order_status_update(
                order_number,
                "Delivery Issue Reported",
                f"Customer {user_id} reported that order was not received. Investigation required.",
                replace_previous=True
            )
        except Exception as e:
            logger.error(f"Failed to send delivery issue notification to tracking bot: {e}")

        # Notify the specific delivery person who handled this order
        try:
            assigned_to = order_data.get('assigned_to')
            logger.info(f"🔍 Looking for assigned delivery person: {assigned_to}")

            if assigned_to:
                from src.utils.delivery_personnel_utils import get_delivery_personnel_by_id
                from src.bots.delivery_bot import delivery_bot

                personnel = get_delivery_personnel_by_id(assigned_to)
                logger.info(f"📋 Found personnel data: {personnel is not None}")

                if personnel and personnel.get('telegram_id'):
                    logger.info(f"📱 Sending notification to delivery person {personnel.get('name')} (ID: {personnel.get('telegram_id')})")
                    # Make the "Complete Order" button reappear for the delivery person
                    complete_markup = types.InlineKeyboardMarkup()
                    complete_btn = types.InlineKeyboardButton(
                        "🏁 Complete Order",
                        callback_data=f"complete_order_{order_number}"
                    )
                    complete_markup.add(complete_btn)

                    # Get comprehensive order details for the delivery person
                    restaurant_id = order_data.get('restaurant_id')
                    restaurant = get_restaurant_by_id(restaurant_id)
                    restaurant_name = restaurant['name'] if restaurant else f"Restaurant {restaurant_id}"

                    delivery_gate = order_data.get('delivery_gate', 'N/A')
                    phone_number = order_data.get('phone_number', 'N/A')
                    total_amount = order_data.get('subtotal', 0) + order_data.get('delivery_fee', 0)

                    # Format items list
                    items = order_data.get('items', [])
                    items_text = ""
                    for item in items:
                        items_text += f"• {item.get('name', 'Unknown Item')} - {item.get('price', 0)} birr\n"

                    # Create comprehensive notification message
                    notification_message = f"""🚨 **DELIVERY ISSUE REPORTED** 🚨

📋 **Order #{order_number}**
🏪 **Restaurant:** {restaurant_name}
📍 **Delivery Address:** {delivery_gate}
📞 **Customer Phone:** {phone_number}

🛍️ **Items Delivered:**
{items_text.strip()}

💰 **Total Amount:** {total_amount} birr

⚠️ **Issue:** Customer reported they did not receive this order
🕐 **Reported at:** {order_data['issue_reported_at']}

🔍 **Action Required:**
1. Contact the customer immediately at {phone_number}
2. Verify the delivery location and status
3. If delivery was incomplete, complete it now
4. Click "Complete Order" below once resolved

📞 **Please call the customer to resolve this issue!**"""

                    delivery_bot.send_message(
                        personnel.get('telegram_id'),
                        notification_message,
                        reply_markup=complete_markup,
                        parse_mode='Markdown'
                    )
                    logger.info(f"✅ Successfully notified delivery person {personnel.get('name', 'Unknown')} about delivery issue for order {order_number}")
                else:
                    logger.warning(f"❌ No personnel found or missing telegram_id for assigned_to: {assigned_to}")
            else:
                logger.warning(f"❌ No assigned_to found in order data for order {order_number}")
        except Exception as e:
            logger.error(f"Failed to notify delivery person about delivery issue: {e}")

        logger.info(f"Customer {user_id} reported delivery issue for order {order_number}")

    except Exception as e:
        logger.error(f"Error in handle_order_not_received: {e}")
        bot.answer_callback_query(call.id, "❌ Error reporting issue")


def register_handlers():
    """Register all handlers in this module"""
    # All handlers are already registered using decorators
    pass
