"""
User profile handlers for the Wiz Aroma Delivery Bot.
Contains handlers for user profile management and points.
"""

from telebot import types

from src.bot_instance import bot
from src.config import logger
from src.data_models import (
    user_points,
)
from src.data_storage import (
    get_order_history,
    get_points_balance,
)
from src.utils.keyboards import (
    get_main_menu_markup,
)


@bot.message_handler(func=lambda message: message.text == "💫 My Points")
def show_points_balance(message):
    """Show user's points balance"""
    try:
        user_id = message.from_user.id
        points_balance = get_points_balance(user_id)

        # Create points balance message
        points_text = (
            "💫 Points Balance\n\n"
            f"You have {points_balance} points\n\n"
            "ℹ️ How to use points:\n"
            "• 1 point = 1 birr for delivery fee\n"
            "• Points can be used to cover delivery fees\n"
            "• You'll earn points on paid delivery fees\n"
            "• Points are earned at (10 + 1)% of delivery fee (decimal part is truncated)\n"
            "• Minimum 1 point earned per delivery"
        )

        # Use simplified markup with just two buttons
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("🍽️ Order Food"),
            types.KeyboardButton("🔙 Back to Main Menu"),
        )

        bot.send_message(message.chat.id, points_text, reply_markup=markup)
    except Exception as e:
        logger.error(f"Error in show_points_balance: {e}")
        bot.send_message(
            message.chat.id, "❌ Error showing points balance. Please try again."
        )


@bot.message_handler(func=lambda message: message.text == "📜 Order History")
def show_order_history(message):
    """Show user's order history"""
    try:
        user_id = message.from_user.id
        order_history = get_order_history(user_id)

        if not order_history:
            bot.send_message(
                message.chat.id,
                "📜 *Your Order History*\n\n"
                "You don't have any previous orders yet.\n\n"
                "Your order history will be displayed here after you place orders.",
                parse_mode="Markdown",
                reply_markup=get_main_menu_markup(),
            )
            return

        # Show the 5 most recent orders
        recent_orders = order_history[:5]
        history_text = "📜 *Your Recent Orders*\n\n"

        for i, order in enumerate(recent_orders):
            # Format the order details
            order_number = order.get("order_number", "Unknown")
            restaurant = order.get("restaurant", "Unknown Restaurant")
            timestamp = order.get("timestamp", "Unknown Time")
            subtotal = order.get("subtotal", 0)
            delivery_fee = order.get("delivery_fee", 0)
            total = subtotal + delivery_fee

            # Count items
            items = order.get("items", [])
            item_count = len(items)

            history_text += f"*Order #{order_number}*\n"
            history_text += f"📅 {timestamp}\n"
            history_text += f"🏪 {restaurant}\n"
            history_text += f"🛒 {item_count} items\n"
            history_text += f"💵 Total: {total} birr\n\n"

            # Add separator between orders
            if i < len(recent_orders) - 1:
                history_text += "-------------------\n\n"

        bot.send_message(
            message.chat.id,
            history_text,
            parse_mode="Markdown",
            reply_markup=get_main_menu_markup(),
        )
    except Exception as e:
        logger.error(f"Error in show_order_history: {e}")
        bot.send_message(
            message.chat.id,
            "❌ Error showing order history. Please try again.",
            reply_markup=get_main_menu_markup(),
        )


def register_handlers():
    """Register all handlers in this module"""
    # All handlers are already registered using decorators
    pass
