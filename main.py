#!/usr/bin/env python
"""
Wiz Aroma Food Delivery System v2.1 - Main Entry Point

This is the main script for the Wiz Aroma Food Delivery System, an enterprise-grade
multi-bot Telegram delivery management platform with Firebase-first architecture,
automatic data lifecycle management, and professional code organization.

Features:
- Multi-bot architecture with specialized functions
- Firebase-first data architecture with automatic cleanup
- Professional data management with standardized patterns
- Broadcast-based order assignment with 5-order limit per delivery personnel
- Point-based payment system with 50% delivery fee sharing
- Comprehensive management bot with analytics and personnel management
- Complete order lifecycle with delivery completion and customer confirmation
- Automatic data lifecycle management and cleanup services

Author: Mihretab Nigatu
Version: 2.1
License: Proprietary
"""

import argparse
import threading
import os
import sys
import telebot
import time
import atexit
import socket
import signal
import json
from telebot.types import BotCommand
from src.firebase_db import (
    get_data,
    get_user_points,
    get_user_names,
    get_user_phone_numbers,
    get_user_emails,
)

# Set the path to the current directory to ensure proper imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Fix for timeout issues - set this before importing bot
telebot.apihelper.READ_TIMEOUT = (
    60  # Increased to 60 seconds to reduce CPU/network usage
)

# Import data models
from src.data_models import (
    delivery_locations_data,
    delivery_fees_data,
    areas_data,
    restaurants_data,
    menus_data,
    user_points,
    user_names,
    user_phone_numbers,
    user_emails,
)

# Import configurations and modules
from src.config import (
    logger,
    DEFAULT_LOG_LEVEL,
    BOT_TOKEN,
    ADMIN_BOT_TOKEN,
    FINANCE_BOT_TOKEN,
    MAINTENANCE_BOT_TOKEN,
    TEST_MODE,
    AREAS_FILE,
    RESTAURANTS_FILE,
    MENUS_FILE,
    DELIVERY_LOCATIONS_FILE,
    DELIVERY_FEES_FILE,
    WATCHDOG_INTERVAL,
)
from src.data_storage import (
    load_user_data,
    save_user_data,
    clean_order_history_data,
    initialize_data_files,
    USE_FIREBASE,
    initialize_areas_from_config,
    initialize_restaurants_from_config,
)
from src.utils.temp_data_manager import initialize_temp_data_manager
from src.utils.auto_cleanup_service import initialize_auto_cleanup_service
from src.utils.firebase_business_data import get_firebase_business_data_manager
from src.bot_instance import (
    bot,
    admin_bot,
    finance_bot,
    maintenance_bot,
    management_bot,
    POLLING_TIMEOUT,
)

# Import new specialized bots
try:
    from src.bots.order_track_bot import order_track_bot
    from src.bots.delivery_bot import delivery_bot
    logger.info("Successfully imported new specialized bots")
except ImportError as e:
    logger.warning(f"Could not import specialized bots: {e}")
    order_track_bot = None
    delivery_bot = None

# Import our custom utilities
from src.utils.error_handling import handle_exceptions, safe_api_call
from src.utils.logging_utils import get_logger
from src.utils.handler_registration import register_all_handlers
from src.utils.data_consistency import get_data_consistency_manager

# Note: Removed hardcoded menu initialization - using Firebase sample data only

# Import handlers package to ensure all handlers are loaded
import src.handlers

# Global flag to indicate shutdown
shutdown_requested = False


def interruptible_sleep(seconds, message="Waiting"):
    """
    Sleep for specified seconds but can be interrupted by shutdown signal.
    Shows progress indicators to keep user informed.

    Args:
        seconds: Number of seconds to sleep
        message: Message to display during sleep
    """
    global shutdown_requested

    if shutdown_requested:
        return False

    logger.info(f"{message}... ({seconds}s)")

    for i in range(seconds):
        if shutdown_requested:
            logger.info("Startup interrupted by shutdown signal")
            return False

        # Show progress every 2 seconds for longer waits
        if seconds > 5 and (i + 1) % 2 == 0:
            remaining = seconds - (i + 1)
            if remaining > 0:
                logger.debug(f"{message}... {remaining}s remaining")

        time.sleep(1)

    return True


def periodic_data_save():
    """Periodically save all user data"""
    try:
        while not shutdown_requested:
            # Save all user data every 5 minutes
            save_user_data()
            logger.info("Periodic data save completed successfully")
            # Sleep for 5 minutes
            for _ in range(300):  # 5 minutes = 300 seconds
                if shutdown_requested:
                    break
                time.sleep(1)
    except Exception as e:
        logger.error(f"Error in periodic data save: {e}")


def run_user_bot():
    """Run the user bot"""
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            # Reset any previous webhook or getUpdates state
            if not TEST_MODE:
                bot.remove_webhook()
                time.sleep(1)  # Increased delay to ensure webhook is removed
                logger.info("User bot is running...")
                try:
                    bot.infinity_polling(
                        timeout=POLLING_TIMEOUT, long_polling_timeout=POLLING_TIMEOUT
                    )
                    return  # If we get here without exception, exit the function successfully
                except telebot.apihelper.ApiException as e:
                    if "Conflict: terminated by other getUpdates request" in str(e):
                        logger.error("=== Bot Conflict Detected ====")
                        logger.error("Another instance of this bot is already running.")
                        logger.error("Killing other processes and retrying...")

                        # Try to kill conflicting processes
                        try:
                            # Windows-specific process termination
                            if os.name == "nt":
                                os.system(
                                    'taskkill /f /im python.exe /fi "WINDOWTITLE eq Telegram Bot"'
                                )

                            # Give time for processes to terminate
                            time.sleep(5)

                            # Increment retry counter
                            retry_count += 1
                            logger.info(f"Retry attempt {retry_count} of {max_retries}")

                            if retry_count >= max_retries:
                                logger.error("Max retry attempts reached. Exiting.")
                                sys.exit(1)

                            # Continue to next retry
                            continue
                        except Exception as kill_error:
                            logger.error(f"Error killing processes: {kill_error}")
                            sys.exit(1)
                    else:
                        logger.error(f"API Exception: {e}")
                        raise
            else:
                logger.info("Test Mode: User bot simulated (not actually running)")
                return
        except Exception as e:
            logger.error(f"User bot polling error: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error("Max retry attempts reached. Exiting.")
                return
            time.sleep(5)  # Wait before retry


def run_admin_bot():
    """Run the admin bot"""
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            # Reset any previous webhook or getUpdates state
            if not TEST_MODE:
                admin_bot.remove_webhook()
                time.sleep(1)  # Increased delay to ensure webhook is removed
                logger.info("Admin bot is running...")
                try:
                    admin_bot.infinity_polling(
                        timeout=POLLING_TIMEOUT, long_polling_timeout=POLLING_TIMEOUT
                    )
                    return  # If we get here without exception, exit the function successfully
                except telebot.apihelper.ApiException as e:
                    if "Conflict: terminated by other getUpdates request" in str(e):
                        logger.error("=== Bot Conflict Detected ====")
                        logger.error("Another instance of this bot is already running.")
                        logger.error("Killing other processes and retrying...")

                        # Try to kill conflicting processes
                        try:
                            # Windows-specific process termination
                            if os.name == "nt":
                                os.system(
                                    'taskkill /f /im python.exe /fi "WINDOWTITLE eq Telegram Bot"'
                                )

                            # Give time for processes to terminate
                            time.sleep(5)

                            # Increment retry counter
                            retry_count += 1
                            logger.info(f"Retry attempt {retry_count} of {max_retries}")

                            if retry_count >= max_retries:
                                logger.error("Max retry attempts reached. Exiting.")
                                sys.exit(1)

                            # Continue to next retry
                            continue
                        except Exception as kill_error:
                            logger.error(f"Error killing processes: {kill_error}")
                            sys.exit(1)
                    else:
                        logger.error(f"API Exception: {e}")
                        raise
            else:
                logger.info("Test Mode: Admin bot simulated (not actually running)")
                return
        except Exception as e:
            logger.error(f"Admin bot polling error: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error("Max retry attempts reached. Exiting.")
                return
            time.sleep(5)  # Wait before retry


def run_finance_bot():
    """Run the finance bot"""
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            # Reset any previous webhook or getUpdates state
            if not TEST_MODE:
                finance_bot.remove_webhook()
                time.sleep(1)  # Increased delay to ensure webhook is removed
                logger.info("Finance bot is running...")
                try:
                    finance_bot.infinity_polling(
                        timeout=POLLING_TIMEOUT, long_polling_timeout=POLLING_TIMEOUT
                    )
                    return  # If we get here without exception, exit the function successfully
                except telebot.apihelper.ApiException as e:
                    if "Conflict: terminated by other getUpdates request" in str(e):
                        logger.error("=== Bot Conflict Detected ====")
                        logger.error("Another instance of this bot is already running.")
                        logger.error("Killing other processes and retrying...")

                        # Try to kill conflicting processes
                        try:
                            # Windows-specific process termination
                            if os.name == "nt":
                                os.system(
                                    'taskkill /f /im python.exe /fi "WINDOWTITLE eq Telegram Bot"'
                                )

                            # Give time for processes to terminate
                            time.sleep(5)

                            # Increment retry counter
                            retry_count += 1
                            logger.info(f"Retry attempt {retry_count} of {max_retries}")

                            if retry_count >= max_retries:
                                logger.error("Max retry attempts reached. Exiting.")
                                sys.exit(1)

                            # Continue to next retry
                            continue
                        except Exception as kill_error:
                            logger.error(f"Error killing processes: {kill_error}")
                            sys.exit(1)
                    else:
                        logger.error(f"API Exception: {e}")
                        raise
            else:
                logger.info("Test Mode: Finance bot simulated (not actually running)")
                return
        except Exception as e:
            logger.error(f"Finance bot polling error: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error("Max retry attempts reached. Exiting.")
                return
            time.sleep(5)  # Wait before retry


def run_maintenance_bot():
    """Run the maintenance bot"""
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            # Reset any previous webhook or getUpdates state
            if not TEST_MODE:
                maintenance_bot.remove_webhook()
                time.sleep(1)  # Increased delay to ensure webhook is removed
                logger.info("Maintenance bot is running...")
                try:
                    maintenance_bot.infinity_polling(
                        timeout=POLLING_TIMEOUT, long_polling_timeout=POLLING_TIMEOUT
                    )
                    return  # If we get here without exception, exit the function successfully
                except telebot.apihelper.ApiException as e:
                    if "Conflict: terminated by other getUpdates request" in str(e):
                        logger.error("=== Bot Conflict Detected ====")
                        logger.error("Another instance of this bot is already running.")
                        logger.error("Killing other processes and retrying...")

                        # Try to kill conflicting processes
                        try:
                            # Windows-specific process termination
                            if os.name == "nt":
                                os.system(
                                    'taskkill /f /im python.exe /fi "WINDOWTITLE eq Telegram Bot"'
                                )

                            # Give time for processes to terminate
                            time.sleep(5)

                            # Increment retry counter
                            retry_count += 1
                            logger.info(f"Retry attempt {retry_count} of {max_retries}")

                            if retry_count >= max_retries:
                                logger.error("Max retry attempts reached. Exiting.")
                                sys.exit(1)

                            # Continue to next retry
                            continue
                        except Exception as kill_error:
                            logger.error(f"Error killing processes: {kill_error}")
                            sys.exit(1)
                    else:
                        logger.error(f"API Exception: {e}")
                        raise
            else:
                logger.info(
                    "Test Mode: Maintenance bot simulated (not actually running)"
                )
                return
        except Exception as e:
            logger.error(f"Maintenance bot polling error: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error("Max retry attempts reached. Exiting.")
                return
            time.sleep(5)  # Wait before retry


def run_management_bot():
    """
    Run the comprehensive management bot for Wiz Aroma v2.0.
    Handles personnel management, analytics, reporting, and system oversight.
    """
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            # Import management bot and register handlers
            from src.bots.management_bot import management_bot, register_management_bot_handlers
            register_management_bot_handlers()
            logger.info("Management bot handlers registered successfully")

            # Reset any previous webhook or getUpdates state
            if not TEST_MODE:
                management_bot.remove_webhook()
                time.sleep(1)  # Increased delay to ensure webhook is removed
                logger.info("Management bot is running...")
                try:
                    management_bot.infinity_polling(
                        timeout=POLLING_TIMEOUT, long_polling_timeout=POLLING_TIMEOUT
                    )
                    return  # If we get here without exception, exit the function successfully
                except telebot.apihelper.ApiException as e:
                    if "Conflict: terminated by other getUpdates request" in str(e):
                        logger.error("=== Bot Conflict Detected ====")
                        logger.error("Another instance of this bot is already running.")
                        logger.error("Killing other processes and retrying...")

                        # Try to kill conflicting processes
                        try:
                            # Windows-specific process termination
                            if os.name == "nt":
                                os.system(
                                    'taskkill /f /im python.exe /fi "WINDOWTITLE eq Telegram Bot"'
                                )

                            # Give time for processes to terminate
                            time.sleep(5)

                            # Increment retry counter
                            retry_count += 1
                            logger.info(f"Retry attempt {retry_count} of {max_retries}")

                            if retry_count >= max_retries:
                                logger.error("Max retry attempts reached. Exiting.")
                                sys.exit(1)

                            # Continue to next retry
                            continue
                        except Exception as kill_error:
                            logger.error(f"Error killing processes: {kill_error}")
                            sys.exit(1)
                    else:
                        logger.error(f"API Exception: {e}")
                        raise
            else:
                logger.info(
                    "Test Mode: Management bot simulated (not actually running)"
                )
                return
        except Exception as e:
            logger.error(f"Management bot polling error: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error("Max retry attempts reached. Exiting.")
                return
            time.sleep(5)  # Wait before retry


def run_order_track_bot():
    """Run the order tracking bot"""
    if order_track_bot is None:
        logger.error("Order tracking bot not available - import failed")
        return

    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            logger.info("Starting order tracking bot...")
            # Start the bot
            order_track_bot.polling(none_stop=True, interval=1)

        except Exception as e:
            retry_count += 1
            logger.error(f"Order tracking bot error (attempt {retry_count}/{max_retries}): {e}")
            if retry_count < max_retries:
                logger.info(f"Retrying in 5 seconds...")
                time.sleep(5)
            else:
                logger.error("Max retry attempts reached. Exiting.")
                return
            time.sleep(5)  # Wait before retry


def run_delivery_bot():
    """Run the delivery bot"""
    if delivery_bot is None:
        logger.error("Delivery bot not available - import failed")
        return

    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            logger.info("Starting delivery bot...")
            # Start the bot
            delivery_bot.polling(none_stop=True, interval=1)

        except Exception as e:
            retry_count += 1
            logger.error(f"Delivery bot error (attempt {retry_count}/{max_retries}): {e}")
            if retry_count < max_retries:
                logger.info(f"Retrying in 5 seconds...")
                time.sleep(5)
            else:
                logger.error("Max retry attempts reached. Exiting.")
                return
            time.sleep(5)  # Wait before retry


def signal_handler(sig, frame):
    """Handle signals for graceful shutdown"""
    global shutdown_requested

    # Check if this is the first shutdown request
    if shutdown_requested:
        logger.warning("🚨 Force shutdown requested! Exiting immediately...")
        sys.exit(1)

    logger.info("🛑 Shutdown signal received, stopping bots gracefully...")
    logger.info("💡 Press Ctrl+C again for immediate force shutdown")

    # Set shutdown flag first to stop any ongoing operations
    shutdown_requested = True

    # Get data consistency manager
    data_manager = get_data_consistency_manager()

    # Stop data refresh thread if running
    try:
        data_manager.stop_refresh_thread()
        logger.info("✅ Data refresh thread stopped")
    except Exception as e:
        logger.error(f"❌ Error stopping data refresh thread: {e}")

    # Save user data before shutting down
    try:
        save_user_data()
        logger.info("✅ User data saved successfully")
    except Exception as e:
        logger.error(f"❌ Error saving user data: {e}")

    # Stop all bots
    try:
        if not TEST_MODE:
            logger.info("🔄 Stopping bot polling...")
            bot.stop_polling()
            admin_bot.stop_polling()
            finance_bot.stop_polling()
            maintenance_bot.stop_polling()
            management_bot.stop_polling()
            logger.info("✅ All bots stopped successfully")
        else:
            logger.info("✅ Test Mode: No bots to stop")
    except Exception as e:
        logger.error(f"❌ Error during bot shutdown: {e}")

    logger.info("🎯 Graceful shutdown complete")
    # Instead of calling exit(), just return to allow main loop to handle exit
    return


def handle_bot_blocked_error(chat_id, user_id):
    """Handle cases where the bot is blocked by the user"""
    logger.warning(f"Bot was blocked by user {user_id} in chat {chat_id}")
    # Clean up any pending orders or user data
    try:
        from src.data_storage import clean_up_order_data

        clean_up_order_data(user_id, None)
    except Exception as e:
        logger.error(f"Error cleaning up data for blocked user {user_id}: {e}")


class ExceptionHandler:
    """Exception handler class for bot exceptions"""

    def __init__(self):
        # Initialize data consistency manager
        self.data_manager = get_data_consistency_manager()

    def handle(self, exception):
        """Handle exception from the bot"""
        # Log the error with detailed information
        logger.error(f"Bot error: {str(exception)}")
        logger.error(f"Exception type: {type(exception).__name__}")

        # Try to get traceback information
        import traceback

        tb = traceback.format_exc()
        logger.error(f"Traceback: {tb}")

        return True  # True to indicate we've handled the exception


# Define a socket for single instance check
USER_BOT_PORT = 47200
ADMIN_BOT_PORT = 47201
FINANCE_BOT_PORT = 47202
MAINTENANCE_BOT_PORT = 47203
MANAGEMENT_BOT_PORT = 47205
ALL_BOTS_PORT = 47204
single_instance_socket = None


def check_single_instance(bot_type):
    """Check if another instance of this specific bot type is already running"""
    global single_instance_socket

    # First check if there are zombie instances
    try:
        # This works for Windows to find any Python processes
        import subprocess

        result = subprocess.run(
            ["tasklist", "/FI", "IMAGENAME eq python.exe", "/FO", "CSV"],
            capture_output=True,
            text=True,
        )

        # Check if there are multiple python processes (excluding this one)
        process_lines = result.stdout.strip().split("\n")[1:]  # Skip header
        bot_processes = len(process_lines)

        if bot_processes > 1:
            logger.warning(f"Found {bot_processes} Python processes running")

            # Try to forcefully kill other python processes if we're desperate
            force_kill = False
            if len(process_lines) > 3:  # Too many processes, force kill
                force_kill = True
                logger.warning("Too many Python processes detected, forcing cleanup")

            if force_kill:
                # Force kill Python processes except the current one
                import os

                current_pid = os.getpid()
                logger.info(f"Current process ID: {current_pid}")

                for line in process_lines:
                    try:
                        if '","' in line:
                            parts = line.strip('"').split('","')
                            if len(parts) >= 2:
                                pid = int(parts[1])
                                if pid != current_pid:
                                    logger.warning(
                                        f"Killing Python process with PID {pid}"
                                    )
                                    subprocess.run(["taskkill", "/F", "/PID", str(pid)])
                    except Exception as e:
                        logger.error(f"Error parsing or killing process: {e}")
    except Exception as e:
        logger.error(f"Error checking for other Python processes: {e}")

    # Select port based on bot type
    if bot_type == "user":
        port = USER_BOT_PORT
    elif bot_type == "admin":
        port = ADMIN_BOT_PORT
    elif bot_type == "finance":
        port = FINANCE_BOT_PORT
    elif bot_type == "maintenance":
        port = MAINTENANCE_BOT_PORT
    elif bot_type == "management":
        port = MANAGEMENT_BOT_PORT
    elif bot_type == "all":
        port = ALL_BOTS_PORT
    else:
        port = USER_BOT_PORT  # Default

    try:
        # Create a socket and try to bind to the port
        single_instance_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            # Set SO_REUSEADDR to allow binding to a port that was recently closed
            single_instance_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

            # Shorter timeout to quickly detect if port is in use
            single_instance_socket.settimeout(1)

            # Try to bind to the port
            single_instance_socket.bind(("localhost", port))
            single_instance_socket.listen(1)

            # Reset timeout to default
            single_instance_socket.settimeout(None)

            # Register cleanup function
            atexit.register(cleanup_socket)

            return True  # We're the only instance of this bot type
        except socket.error as e:
            # Try more aggressively to cleanup
            try:
                # Close the socket we attempted to create
                if single_instance_socket:
                    single_instance_socket.close()

                # Force kill any Python processes that might be using the port
                # (could be zombie processes)
                import subprocess

                subprocess.run(
                    [
                        "taskkill",
                        "/F",
                        "/IM",
                        "python.exe",
                        "/FI",
                        f"WINDOWTITLE eq Telegram Bot",
                    ],
                    capture_output=True,
                )

                # Try to bind again after killing potential zombies
                time.sleep(2)  # Wait for processes to fully terminate
                single_instance_socket = socket.socket(
                    socket.AF_INET, socket.SOCK_STREAM
                )
                single_instance_socket.setsockopt(
                    socket.SOL_SOCKET, socket.SO_REUSEADDR, 1
                )
                single_instance_socket.bind(("localhost", port))
                single_instance_socket.listen(1)
                atexit.register(cleanup_socket)

                logger.info("Successfully bound to port after cleanup")
                return True
            except socket.error:
                # Still couldn't bind after cleanup attempts
                logger.error(
                    f"=== CRITICAL ERROR: Another instance of the {bot_type} bot is already running ===="
                )
                logger.error(
                    f"Please close the other {bot_type} bot instance before starting a new one."
                )
                return False
    except Exception as e:
        logger.error(f"Error in single instance check: {e}")
        return False


def cleanup_socket():
    """Clean up the socket on exit"""
    global single_instance_socket
    if single_instance_socket:
        try:
            single_instance_socket.close()
        except:
            pass


def run_watchdog():
    """
    Watchdog function that monitors the health of the bot processes
    and restarts them if they appear to be stalled or crashed
    """
    global shutdown_requested

    # Initialize health indicators
    last_activity_time = time.time()
    health_checks = {
        "user_bot": {"active": False, "last_poll": 0},
        "admin_bot": {"active": False, "last_poll": 0},
        "finance_bot": {"active": False, "last_poll": 0},
        "maintenance_bot": {"active": False, "last_poll": 0},
    }

    # Get data consistency manager
    data_manager = get_data_consistency_manager()

    # Create proper custom filter class with a key attribute
    class HealthCheckFilter(telebot.custom_filters.SimpleCustomFilter):
        key = "health_check"

        def __init__(self, bot_type):
            self.bot_type = bot_type

        def check(self, message):
            health_checks[self.bot_type]["last_poll"] = time.time()
            health_checks[self.bot_type]["active"] = True
            return True

    # Register health check with each bot
    if not TEST_MODE:
        bot.add_custom_filter(HealthCheckFilter("user_bot"))
        admin_bot.add_custom_filter(HealthCheckFilter("admin_bot"))
        finance_bot.add_custom_filter(HealthCheckFilter("finance_bot"))
        maintenance_bot.add_custom_filter(HealthCheckFilter("maintenance_bot"))

    logger.info("Started watchdog timer")

    # Main watchdog loop
    while not shutdown_requested:
        try:
            time.sleep(30)  # Check every 30 seconds

            # Skip health check if shutdown is requested
            if shutdown_requested:
                break

            current_time = time.time()

            # Check data consistency manager heartbeat
            if not data_manager.is_alive():
                logger.warning(
                    "Watchdog: Data consistency manager appears stalled (no heartbeat)"
                )

                try:
                    # Try to restart data refresh thread
                    logger.info("Watchdog: Restarting data consistency manager...")
                    data_manager.stop_refresh_thread()
                    time.sleep(1)
                    data_manager.start_refresh_thread()
                    data_manager.heartbeat()  # Update heartbeat
                    logger.info("Watchdog: Data consistency manager restarted")
                except Exception as e:
                    logger.error(
                        f"Watchdog: Failed to restart data consistency manager: {e}"
                    )

            # Check if any active bots haven't received a health update in 5 minutes
            for bot_type, health in health_checks.items():
                if (
                    health["active"] and (current_time - health["last_poll"]) > 300
                ):  # 5 minutes
                    logger.warning(
                        f"Watchdog: {bot_type} appears to be stalled (no activity for 5 minutes)"
                    )

                    # Try to recover the specific bot
                    if bot_type == "user_bot":
                        try:
                            logger.info("Watchdog: Restarting user bot...")
                            if hasattr(bot, "stop_polling"):
                                bot.stop_polling()
                            # Start a new thread for the user bot
                            user_bot_thread = threading.Thread(target=run_user_bot)
                            user_bot_thread.daemon = True
                            user_bot_thread.start()
                        except Exception as e:
                            logger.error(f"Watchdog: Failed to restart user bot: {e}")

                    # Reset the health check
                    health["active"] = False

            # Periodically save data as a backup measure
            if (current_time - last_activity_time) > 300:  # 5 minutes
                try:
                    save_user_data()
                    logger.info("Watchdog: Saved user data as precaution")
                    last_activity_time = current_time
                except Exception as e:
                    logger.error(f"Watchdog: Error saving user data: {e}")

        except Exception as e:
            logger.error(f"Watchdog: Error in watchdog timer: {e}")
            # Continue running the watchdog even if there's an error

    logger.info("Watchdog timer stopped")


# Define a function to refresh user-specific data
def refresh_user_data():
    """Refresh user-specific data from Firebase"""
    try:
        # Check if Firebase is enabled
        from src.data_storage import USE_FIREBASE
        if not USE_FIREBASE:
            logger.debug("Firebase disabled - skipping user data refresh")
            return

        # Get fresh data from Firebase
        global user_points, user_names, user_phone_numbers

        # Initialize counters
        points_count = 0
        names_count = 0
        phone_count = 0

        # Refresh points data
        points_data = get_user_points()
        if points_data:
            user_points.update(points_data)
            points_count = len(points_data)
            logger.debug(f"Refreshed points data for {points_count} users")

        # Refresh user names
        names_data = get_user_names()
        if names_data:
            user_names.update(names_data)
            names_count = len(names_data)
            logger.debug(f"Refreshed user names for {names_count} users")

        # Refresh phone numbers
        phone_numbers_data = get_user_phone_numbers()
        if phone_numbers_data:
            user_phone_numbers.update(phone_numbers_data)
            phone_count = len(phone_numbers_data)
            logger.debug(f"Refreshed phone numbers for {phone_count} users")

        # Log a single summary message instead of multiple detailed ones
        logger.info(
            f"Refreshed user data from Firebase - Points: {points_count} users, Names: {names_count} users, Phone numbers: {phone_count} users"
        )
    except Exception as e:
        logger.error(f"Error in refresh_user_data: {e}")


def main():
    """Main function to run the bot"""
    # Setup argument parsing for running specific bots
    parser = argparse.ArgumentParser(description="Wiz Aroma Delivery Bot")
    parser.add_argument(
        "--bot",
        choices=["user", "admin", "finance", "maintenance", "management", "order_track", "delivery", "all"],
        default="all",
        help="Specify which bot to run (default: all)",
    )
    parser.add_argument(
        "--test",
        action="store_true",
        help="Run in test mode without connecting to Telegram",
    )
    parser.add_argument(
        "--update-menus",
        action="store_true",
        help="Update menus from configuration and exit",
    )
    parser.add_argument(
        "--initialize-data",
        action="store_true",
        help="Initialize all data files and exit",
    )
    args = parser.parse_args()
    selected_bot = args.bot
    test_mode = args.test
    update_menus = args.update_menus
    initialize_data = args.initialize_data

    # Sleep for a moment to let any terminating processes fully exit
    time.sleep(2)

    # Check if another instance is already running
    if not check_single_instance(selected_bot):
        logger.error("Exiting due to another instance already running.")
        return 1

    # Verify bot tokens exist
    from src.config import MANAGEMENT_BOT_TOKEN
    if not TEST_MODE and not all(
        [BOT_TOKEN, ADMIN_BOT_TOKEN, FINANCE_BOT_TOKEN, MAINTENANCE_BOT_TOKEN, MANAGEMENT_BOT_TOKEN]
    ):
        logger.error("Bot tokens not found. Please check your .env file.")
        return 1

    logger.info("Starting bots...")
    if TEST_MODE:
        logger.info("Running in TEST MODE - Telegram functionality will be limited")

    # Note: Using Firebase sample data only - no hardcoded menu initialization
    logger.info("Using Firebase sample data for all menus")

    # Clean up old favorite orders data
    clean_order_history_data()
    logger.info("Cleaned up old favorite orders data")

    # Initialize modern data management systems
    try:
        logger.info("🔧 Initializing temporary data management...")
        initialize_temp_data_manager()

        logger.info("🧹 Initializing automatic cleanup service...")
        initialize_auto_cleanup_service()

        logger.info("🔥 Initializing Firebase business data manager...")
        firebase_manager = get_firebase_business_data_manager()

        logger.info("✅ Modern data management systems initialized")
    except Exception as e:
        logger.error(f"Error initializing data management systems: {e}")
        logger.warning("Continuing with legacy data management")

    # Load user data
    try:
        load_user_data()
        logger.info("User data loaded successfully")

        # Make sure areas and restaurants are initialized
        initialize_areas_from_config()
        initialize_restaurants_from_config()
        logger.info("Ensured areas and restaurants are initialized")
    except Exception as e:
        logger.error(f"Error loading user data: {e}")
        logger.warning("Starting with empty user data.")

    # Set up error handling
    exception_handler = ExceptionHandler()
    bot.exception_handler = exception_handler
    admin_bot.exception_handler = exception_handler
    finance_bot.exception_handler = exception_handler
    maintenance_bot.exception_handler = exception_handler

    # Register all handlers using our centralized handler registration system
    bot_instances = {
        "user": bot,
        "admin": admin_bot,
        "finance": finance_bot,
        "maintenance": maintenance_bot,
    }
    register_all_handlers(bot_instances)
    logger.info("All handlers registered successfully")

    # Register slash commands for the user bot
    user_commands = [
        BotCommand("start", "Start the bot and see welcome message"),
        BotCommand("help", "Show help information"),
        BotCommand("order", "Start placing a new order"),
        BotCommand("points", "Check your points balance"),
        BotCommand("favorite", "Show your favorite orders"),
    ]
    bot.set_my_commands(user_commands)

    if TEST_MODE:
        logger.info("Test mode enabled - displaying test status message")
        logger.info("System initialized successfully")
        logger.info("Data loading complete")
        logger.info("All handlers registered")
        logger.info(
            "Project is running in test mode - no Telegram functionality available"
        )
        return 0

    # Initialize and start data consistency manager
    data_manager = get_data_consistency_manager()

    # Define a refresh function that doesn't use get_favorite_orders without a user_id
    def refresh_config_data():
        global areas_data, restaurants_data, menus_data, delivery_locations_data, delivery_fees_data

        try:
            if USE_FIREBASE:
                # Load configuration data only
                try:
                    areas_data = get_data("/areas") or {"areas": []}
                except Exception as e:
                    logger.error(f"Error refreshing areas data: {e}")

                try:
                    restaurants_data = get_data("/restaurants") or {"restaurants": []}
                except Exception as e:
                    logger.error(f"Error refreshing restaurants data: {e}")

                try:
                    menus_data = get_data("/menus") or {
                        "default_menu_items": [],
                        "restaurant_menus": {},
                    }
                except Exception as e:
                    logger.error(f"Error refreshing menus data: {e}")

                try:
                    delivery_locations_data = get_data("/delivery_locations") or {
                        "delivery_locations": []
                    }
                except Exception as e:
                    logger.error(f"Error refreshing delivery locations data: {e}")

                try:
                    delivery_fees_data = get_data("/delivery_fees") or {
                        "delivery_fees": []
                    }
                except Exception as e:
                    logger.error(f"Error refreshing delivery fees data: {e}")

                logger.info("Refreshed configuration data from Firebase")
            else:
                # Load from JSON files (these don't use get_favorite_orders)
                # Areas data
                if os.path.exists(AREAS_FILE):
                    try:
                        with open(
                            AREAS_FILE, "r", encoding="utf-8", errors="replace"
                        ) as f:
                            loaded_areas = json.load(f)
                            areas_data.update(loaded_areas)
                    except json.JSONDecodeError:
                        logger.error(
                            f"Error loading {AREAS_FILE}. File may be corrupted."
                        )

                # Restaurants data
                if os.path.exists(RESTAURANTS_FILE):
                    try:
                        with open(
                            RESTAURANTS_FILE,
                            "r",
                            encoding="utf-8",
                            errors="replace",
                        ) as f:
                            loaded_restaurants = json.load(f)
                            restaurants_data.update(loaded_restaurants)
                    except json.JSONDecodeError:
                        logger.error(
                            f"Error loading {RESTAURANTS_FILE}. File may be corrupted."
                        )

                # Menus data
                if os.path.exists(MENUS_FILE):
                    try:
                        with open(
                            MENUS_FILE, "r", encoding="utf-8", errors="replace"
                        ) as f:
                            loaded_menus = json.load(f)
                            menus_data.update(loaded_menus)
                    except json.JSONDecodeError:
                        logger.error(
                            f"Error loading {MENUS_FILE}. File may be corrupted."
                        )

                # Delivery locations data
                if os.path.exists(DELIVERY_LOCATIONS_FILE):
                    try:
                        with open(
                            DELIVERY_LOCATIONS_FILE,
                            "r",
                            encoding="utf-8",
                            errors="replace",
                        ) as f:
                            loaded_locations = json.load(f)
                            delivery_locations_data.update(loaded_locations)
                    except json.JSONDecodeError:
                        logger.error(
                            f"Error loading {DELIVERY_LOCATIONS_FILE}. File may be corrupted."
                        )

                # Delivery fees data
                if os.path.exists(DELIVERY_FEES_FILE):
                    try:
                        with open(
                            DELIVERY_FEES_FILE,
                            "r",
                            encoding="utf-8",
                            errors="replace",
                        ) as f:
                            loaded_fees = json.load(f)
                            delivery_fees_data.update(loaded_fees)
                    except json.JSONDecodeError:
                        logger.error(
                            f"Error loading {DELIVERY_FEES_FILE}. File may be corrupted."
                        )

                logger.info("Refreshed configuration data from JSON files")
        except Exception as e:
            logger.error(f"Error in refresh_config_data: {e}")
            # Continue running even if refresh fails
            logger.warning("Data refresh failed but bot will continue running")

    # Register data types for consistency management
    data_manager.register_data_type("areas", 300, lambda: refresh_config_data())
    data_manager.register_data_type("restaurants", 300, lambda: refresh_config_data())
    data_manager.register_data_type("menus", 300, lambda: refresh_config_data())
    data_manager.register_data_type(
        "delivery_locations", 300, lambda: refresh_config_data()
    )
    data_manager.register_data_type("delivery_fees", 300, lambda: refresh_config_data())

    # Register user data for more frequent updates (every 60 seconds)
    data_manager.register_data_type("user_points", 60, lambda: refresh_user_data())

    # Start data refresh thread
    data_manager.start_refresh_thread()
    logger.info("Data consistency manager started")

    # Create and start data save thread
    data_save_thread = threading.Thread(target=periodic_data_save)
    data_save_thread.daemon = True
    data_save_thread.start()

    # Create and start watchdog thread
    watchdog_thread = threading.Thread(target=run_watchdog)
    watchdog_thread.daemon = True
    watchdog_thread.start()
    logger.info("Watchdog thread started")

    # Start the selected bot based on command line argument
    if selected_bot == "all":
        logger.info("🚀 Starting all bots with staggered startup to prevent API conflicts...")
        logger.info("📝 Note: This process takes ~30 seconds. Press Ctrl+C to cancel if needed.")

        # Define bot configurations for organized startup
        bot_configs = [
            ("User Bot", threading.Thread(target=run_user_bot), "👤"),
            ("Admin Bot", threading.Thread(target=run_admin_bot), "⚙️"),
            ("Finance Bot", threading.Thread(target=run_finance_bot), "💰"),
            ("Maintenance Bot", threading.Thread(target=run_maintenance_bot), "🔧"),
            ("Management Bot", threading.Thread(target=run_management_bot), "📊"),
            ("Order Track Bot", threading.Thread(target=run_order_track_bot), "📦"),
            ("Delivery Bot", threading.Thread(target=run_delivery_bot), "🚚"),
        ]

        # Set all threads as daemon threads
        for name, thread, icon in bot_configs:
            thread.daemon = True

        # Start bots with improved progress indicators and shorter delays
        total_bots = len(bot_configs)
        for i, (name, thread, icon) in enumerate(bot_configs, 1):
            if shutdown_requested:
                logger.info("❌ Bot startup cancelled by user")
                return 1

            logger.info(f"{icon} Starting {name} ({i}/{total_bots})...")
            thread.start()

            # Shorter delay between bot starts (5s instead of 10s)
            # Skip delay for the last bot
            if i < total_bots:
                delay_msg = f"⏳ Waiting before starting next bot ({i}/{total_bots} started)"
                if not interruptible_sleep(5, delay_msg):
                    logger.info("❌ Bot startup cancelled during delay")
                    return 1

        logger.info("✅ All bots started successfully!")
        logger.warning("⚠️  Running all bots simultaneously may cause API conflicts")
    elif selected_bot == "user":
        logger.info("👤 Starting user bot...")
        user_bot_thread = threading.Thread(target=run_user_bot)
        user_bot_thread.daemon = True
        user_bot_thread.start()
    elif selected_bot == "admin":
        logger.info("⚙️ Starting admin bot...")
        admin_bot_thread = threading.Thread(target=run_admin_bot)
        admin_bot_thread.daemon = True
        admin_bot_thread.start()
    elif selected_bot == "finance":
        logger.info("💰 Starting finance bot...")
        finance_bot_thread = threading.Thread(target=run_finance_bot)
        finance_bot_thread.daemon = True
        finance_bot_thread.start()
    elif selected_bot == "maintenance":
        logger.info("🔧 Starting maintenance bot...")
        maintenance_bot_thread = threading.Thread(target=run_maintenance_bot)
        maintenance_bot_thread.daemon = True
        maintenance_bot_thread.start()
    elif selected_bot == "management":
        logger.info("📊 Starting management bot...")
        management_bot_thread = threading.Thread(target=run_management_bot)
        management_bot_thread.daemon = True
        management_bot_thread.start()
    elif selected_bot == "order_track":
        logger.info("📦 Starting order tracking bot...")
        order_track_bot_thread = threading.Thread(target=run_order_track_bot)
        order_track_bot_thread.daemon = True
        order_track_bot_thread.start()
    elif selected_bot == "delivery":
        logger.info("🚚 Starting delivery bot...")
        delivery_bot_thread = threading.Thread(target=run_delivery_bot)
        delivery_bot_thread.daemon = True
        delivery_bot_thread.start()

    logger.info(f"✅ Started {selected_bot} bot successfully")

    logger.info("🎉 All bots and data save thread started successfully")
    logger.info("🔄 System is now running. Press Ctrl+C to stop gracefully.")

    # Add a small delay to let all bots fully initialize
    if not interruptible_sleep(2, "🔧 Finalizing bot initialization"):
        return 1

    # Register signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    logger.info("🚀 Wiz-Aroma bot system is fully operational!")
    logger.info("📱 Users can now interact with the bots")

    # Keep the main thread running with better interruption handling
    try:
        while not shutdown_requested:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("⌨️  Keyboard interrupt received, initiating graceful shutdown...")
        signal_handler(None, None)
        return 0

    logger.info("🏁 Main loop exited, system shutdown complete")
    return 0


if __name__ == "__main__":
    sys.exit(main())